{"name": "adsLayoutBackend", "version": "1.37.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node --require dd-trace/init dist/main", "lint": "eslint \"{src,test}/**/*.ts\"", "lint:fix": "npm run lint -- --fix", "pretty": "npx prettier src test --check", "pretty:fix": "npm run pretty -- --write", "format": "npm run prettier:fix && npm run lint:fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@fastify/static": "^6.10.1", "@nestjs/common": "^10.4.15", "@nestjs/core": "^10.4.15", "@nestjs/mapped-types": "^2.0.6", "@nestjs/mongoose": "^10.1.0", "@nestjs/platform-fastify": "^10.4.15", "@nestjs/schedule": "^4.1.2", "@nestjs/swagger": "^8.1.0", "@types/crypto-js": "^4.2.2", "ads-layouts-tools": "^1.6.0", "class-validator": "^0.14.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.8", "dd-trace": "^4.11.1", "dotenv": "^16.0.3", "envalid": "^8.0.0", "fastify-swagger": "^5.2.0", "joi": "^17.9.2", "json-rules-engine": "^7.3.0", "lodash": "^4.17.21", "mongoose": "^7.8.7", "nestjs-joi": "^1.9.0", "node-color-log": "^12.0.1", "node-fetch": "^2.7.0", "reflect-metadata": "^0.1.13", "rimraf": "^5.0.0", "rxjs": "^7.8.1", "uuid": "^9.0.0", "winston": "^3.9.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^9.1.0", "@nestjs/testing": "^10.4.15", "@types/cron": "^2.4.0", "@types/jest": "^29.5.1", "@types/lodash": "^4.17.16", "@types/node": "^20.1.0", "@types/node-fetch": "^2.6.12", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.3.1", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-unused-imports": "^3.1.0", "jest": "^29.5.0", "mongodb-memory-server": "^10.1.4", "prettier": "3.2.5", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.4"}, "engines": {"node": ">=22.0.0"}}