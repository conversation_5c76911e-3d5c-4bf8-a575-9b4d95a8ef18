{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strict": true, "noImplicitAny": true, "useUnknownInCatchVariables": false, "skipLibCheck": true, "esModuleInterop": true, "paths": {"InterfacesAndTypes": ["src/interfacesAndTypes"], "SwaggerModels/*": ["src/swaggerModels/*"], "Utils/*": ["src/utils/*"], "Utils": ["src/utils"], "Helpers/*": ["src/helpers/*"], "Helpers": ["src/helpers"], "Errors/*": ["src/errors/*"], "Logger/*": ["src/logger/*"]}}}