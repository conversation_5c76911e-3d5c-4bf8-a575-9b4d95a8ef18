import { Controller, Get } from '@nestjs/common';
import { LogLevel } from 'ads-layouts-tools';
import { formatPerformanceTime } from 'Helpers';
import { ReleaseVersionNewnessInterface } from 'InterfacesAndTypes';
import { CreateException, log } from 'Utils';
import { AdConfigService } from '../adConfigs/adConfig.service';
import { DisplayConfigService } from './displayConfig.service';

@Controller('displayConfig')
export class DisplayConfigController {
  constructor(
    private readonly displayConfigService: DisplayConfigService,
    private readonly adConfigService: AdConfigService
  ) {}

  // CREATE FROM FILE
  @Get('/load')
  async setupDisplayConfig() {
    const getCurrentPerformanceTime = () => Date.now();
    const processingTime0 = getCurrentPerformanceTime();

    try {
      const response = await this.displayConfigService.setupDisplayConfig();

      const processingTime1 = getCurrentPerformanceTime();

      const processingTime = formatPerformanceTime(processingTime0, processingTime1);

      return {
        processingTime,
        loadStatus: response
      };
    } catch (error) {
      const errMsg = 'ERROR_CANNOT_LOAD_DISPLAY_CONFIG';
      log(errMsg, { error }, LogLevel.error);

      throw CreateException(
        { ...error, message: errMsg, statusCode: 400 }, // TODO: modify structure of Error
        { cause: new Error(error) }
      );
    }
  }

  @Get('/getUniqueServicesList')
  async getUniqueServicesList() {
    try {
      return await this.displayConfigService.getUniqueServicesList();
    } catch (error) {
      throw CreateException(error);
    }
  }

  @Get('/getUniqueSiteVersions')
  async getUniqueSiteVersions(): Promise<string[]> {
    const response = await this.displayConfigService.getUniqSiteVersions();
    return [...response];
  }

  @Get('/getUniqueSiteVersionsWithStatus')
  async getUniqueSiteVersionsWithStatus(): Promise<ReleaseVersionNewnessInterface[]> {
    const adConfigReleaseVersionAmount =
      await this.adConfigService.countAdConfigReleaseVersions();

    const uniqueSiteVersions = await this.displayConfigService.getUniqSiteVersions();

    return this.displayConfigService.getUniqueSiteVersionsWithStatus(
      uniqueSiteVersions,
      adConfigReleaseVersionAmount
    );
  }
}
