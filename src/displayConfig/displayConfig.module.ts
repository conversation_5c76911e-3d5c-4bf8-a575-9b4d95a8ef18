import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DisplayConfig,
  DisplayConfigFiltersModule,
  DisplayConfigSchema
} from 'ads-layouts-tools';
import { log } from 'Utils';
import { AdConfigModule } from '../adConfigs/adConfig.module';
import { DisplayConfigController } from './displayConfig.controller';
import { DisplayConfigService } from './displayConfig.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: DisplayConfig.name,
        schema: DisplayConfigSchema
      }
    ]),
    AdConfigModule,
    DisplayConfigFiltersModule.configure(log)
  ],
  controllers: [DisplayConfigController],
  providers: [DisplayConfigService]
})
export class DisplayConfigModule {}
