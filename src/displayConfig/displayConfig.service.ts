import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  DisplayConfig,
  DisplayConfigDocument,
  DisplayConfigFiltersService,
  DisplayType,
  LogLevel
} from 'ads-layouts-tools';
import { extractReleases, union } from 'Helpers';
import { ReleaseVersionNewnessInterface, VersionComparisonResponse } from 'InterfacesAndTypes';
import _ from 'lodash';
import { Model } from 'mongoose';
import { log } from 'Utils';
import { AdConfigReleaseVersionAmount } from '../adConfigs/schema';
import { validators } from '../envalidConfig';

@Injectable()
export class DisplayConfigService {
  constructor(
    @InjectModel(DisplayConfig.name)
    private displayConfigModel: Model<DisplayConfigDocument>,
    private readonly displayConfigFiltersService: DisplayConfigFiltersService
  ) {}

  private async deleteAllDisplayConfigs() {
    const allDisplayConfigs = await this.displayConfigModel.deleteMany();

    log('DELETE_ALL_DISPLAY_CONFIGS_STATUS', { status: allDisplayConfigs });
  }

  async setupDisplayConfig() {
    log('DISPLAY_CONFIG_LOAD_INVOKED');
    await this.deleteAllDisplayConfigs();

    const displayConfigData = await this.displayConfigFiltersService.getLambdaDisplayConfigs(
      validators.DISPLAY_CONFIG_URL
    );

    const adConfigsUrlsWithDisplayConfigData =
      await this.displayConfigFiltersService.parseSDKLambdaDisplayConfigOutput(
        displayConfigData
      );

    const insert = await this.displayConfigModel.insertMany(
      adConfigsUrlsWithDisplayConfigData
    );

    log('DISPLAY_CONFIG_LOAD_SUCCESS', {
      insertLength: insert.length.toString()
    });
  }

  async getUniqueServicesList(): Promise<string[]> {
    const list = await this.displayConfigModel.find().select('service');

    return [...new Set(list.map(singleService => singleService.service))];
  }

  async getUniqSiteVersions(): Promise<Set<string>> {
    const { service } = await this.displayConfigFiltersService.getLambdaDisplayConfigs(
      validators.DISPLAY_CONFIG_URL
    );
    let uniqueReleases: Set<string> = new Set();

    for (const key in service) {
      const serviceData = service[key].envs;
      const extension = extractReleases(serviceData);
      uniqueReleases = union(uniqueReleases, extension);
    }

    return uniqueReleases;
  }
  getUniqueSiteVersionsWithStatus(
    uniqueDisplayConfigReleases: Set<string>,
    adConfigReleasesAmounts: AdConfigReleaseVersionAmount[]
  ): ReleaseVersionNewnessInterface[] {
    const trimLastForwardSlash = (version: string) => {
      return version.endsWith('/') ? version.substring(0, version.length - 1) : version;
    };

    return Array.from(uniqueDisplayConfigReleases).map(displayVersion => {
      displayVersion = trimLastForwardSlash(displayVersion);

      const isNew = !adConfigReleasesAmounts.some(adItem => {
        const adVersion = trimLastForwardSlash(adItem.version);
        return adVersion === displayVersion;
      });

      return { isNew, version: displayVersion };
    });
  }

  async compareLambdaAndDBVersions(): Promise<VersionComparisonResponse> {
    try {
      const dbConfigs = await this.displayConfigModel.find().lean();
      if (!dbConfigs.length) {
        throw new Error(`No display config found`);
      }

      const displayConfigData = await this.displayConfigFiltersService.getLambdaDisplayConfigs(
        validators.DISPLAY_CONFIG_URL
      );
      const configsFromLambda =
        await this.displayConfigFiltersService.parseSDKLambdaDisplayConfigOutput(
          displayConfigData
        );

      if (!configsFromLambda.length) {
        throw new Error(`No display config found in SDK Lambda`);
      }

      const comparison = this.compareConfigs(configsFromLambda, dbConfigs);

      return {
        comparison,
        lambdaSDKVersions: configsFromLambda,
        AdsLayoutsDBVersions: dbConfigs
      };
    } catch (error) {
      log('ERROR_COMPARE_VERSIONS', { error }, LogLevel.error);
      throw error;
    }
  }

  async compareLambdaAndDBSpecificVersion(serviceIds: string[]) {
    const configsFromDB = await this.displayConfigModel.find({ service: serviceIds }).lean();
    if (!configsFromDB.length) {
      throw new Error(`No display config found for service ID: ${serviceIds}`);
    }

    const displayConfigData = await this.displayConfigFiltersService.getLambdaDisplayConfigs(
      validators.DISPLAY_CONFIG_URL
    );

    const displayConfigDataCopy = _.cloneDeep(displayConfigData);
    displayConfigDataCopy.service = {};

    serviceIds.forEach(serviceId => {
      displayConfigDataCopy.service[serviceId] = displayConfigData.service[serviceId];
    });

    const configsFromLambda =
      await this.displayConfigFiltersService.parseSDKLambdaDisplayConfigOutput(
        displayConfigDataCopy
      );

    if (!configsFromLambda.length) {
      throw new Error(`No display config found for service ID: ${serviceIds} in Lambda`);
    }

    const comparison = this.compareConfigs(configsFromLambda, configsFromDB);

    return {
      comparison,
      lambdaSDKVersions: configsFromLambda,
      AdsLayoutsDBVersions: configsFromDB
    };
  }

  makeKey = ({ service, env, siteVersion, release }: DisplayType): string =>
    [service, env, siteVersion, release].join('__');

  compareConfigs(array1: DisplayType[], array2: DisplayType[]): boolean {
    const set1 = new Set(array1.map(this.makeKey));
    const set2 = new Set(array2.map(this.makeKey));

    return _.isEqual(set1, set2);
  }
}
