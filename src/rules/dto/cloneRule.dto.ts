import { Rule } from 'ads-layouts-tools';
import * as <PERSON><PERSON> from 'joi';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';

type ruleModifiers = Partial<
  Omit<Rule, 'name' | 'rulesPackage' | 'conditions' | 'event' | 'conditionsName' | 'eventName'>
>;

export type cloneRule = {
  name: string;
  newName: string;
  rulesPackage?: string;
  modifiers?: ruleModifiers;
};

const ruleModifiersSchema = Joi.object<ruleModifiers>({
  pageType: Joi.array().items(Joi.string().required()).optional(),
  locationInfoPageType: Joi.array().items(Joi.string().required()).optional(),
  serviceId: Joi.array().items(Joi.string().required()).optional(),
  layout: Joi.array().items(Joi.string().required()).optional(),
  enabled: Joi.boolean().optional(),
  sectionId: Joi.array().items(Joi.string().required()).optional(),
  pageId: Joi.array().items(Joi.string().required()).optional(),
  priority: Joi.number().optional(),
  siteVersion: Joi.array().items(Joi.string().required()).optional(),
  paywall: Joi.array().items(Joi.string().required()).optional(),
  accessModel: Joi.array().items(Joi.string().required()).optional()
});

@JoiSchemaOptions({
  allowUnknown: false,
  abortEarly: false
})
export class cloneRuleDTO implements cloneRule {
  @JoiSchema(Joi.string().required())
  name!: string;

  @JoiSchema(Joi.string().required())
  newName!: string;

  @JoiSchema(Joi.string().optional())
  rulesPackage?: string;

  @JoiSchema(ruleModifiersSchema.optional())
  modifiers?: ruleModifiers;
}
