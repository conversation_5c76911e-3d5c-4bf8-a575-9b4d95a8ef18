import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  Req
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LogLevel, ResponseInterface, Rule } from 'ads-layouts-tools';
import { FastifyRequest } from 'fastify';
import { getClientIp, searchQueryNameDto } from 'Helpers';
import { IRequestData } from 'InterfacesAndTypes';
import { QueryOptions } from 'mongoose';
import { CreateException, log } from 'Utils';
import { cloneRuleDTO } from './dto/cloneRule.dto';
import { RuleService } from './rules.service';

@Controller('rule')
@ApiTags('Rule')
export class RuleController {
  constructor(private readonly ruleService: RuleService) {}

  getRequestData(req: FastifyRequest): IRequestData {
    const reqIP = getClientIp(req);
    return { reqIP, reqHeaders: req.headers };
  }

  // CREATE
  @Post()
  async createRule(@Req() req: FastifyRequest, @Body() rule: Rule) {
    const reqInfo = this.getRequestData(req);

    log('RULE_CREATE_REQUEST', { ...reqInfo, rule });

    try {
      if (rule.rulesPackage) {
        throw {
          message:
            "Can't pass rulesPackage name while create new Rule. It will be always 'original' and not set in new document",
          statusCode: HttpStatus.BAD_REQUEST
        };
      }

      const createdRule = await this.ruleService.createRule(rule);
      const response = {
        status: 'rule created successfully',
        createdRuleDetails: createdRule
      };
      log('RULE_CREATE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_CREATE_RULE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // READ
  @Get()
  async getRules(@Req() req: FastifyRequest, @Query() searchQuery: searchQueryNameDto) {
    const reqInfo = this.getRequestData(req);

    log('RULE_GET_ALL_REQUEST', { ...reqInfo });

    try {
      const ruleData = await this.ruleService.getAllRules(searchQuery);

      return {
        message: 'All rules data found successfully',
        ruleData
      };
    } catch (error) {
      log('ERROR_RULE_GET_ALL', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // READ
  @Get('/rulesWithAdConfigGroup')
  async getRulesWithPlaceholdersGroups(@Req() req: FastifyRequest) {
    const reqInfo = this.getRequestData(req);

    log('RULE_GET_ALL_REQUEST', { ...reqInfo });

    try {
      const ruleData = await this.ruleService.getRulesWithPlaceholdersGroups();

      return {
        message: 'All rules data found successfully',
        ruleData: ruleData.map(rd => {
          const { event, name, ...others } = (rd as any)._doc;
          return {
            ...others,
            ruleName: name,
            eventName: event.name,
            eventDesc: event.desc,
            adConfigGroup: event.params.adConfigGroup
          };
        })
      };
    } catch (error) {
      log('ERROR_RULE_GET_ALL', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Get('/:name/:rulesPackage?')
  async getRule(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined,
    @Query() searchQuery: searchQueryNameDto
  ) {
    const reqInfo = this.getRequestData(req);

    log('RULE_GET_BY_NAME_REQUEST', { ...reqInfo, name, rulesPackage });

    try {
      const existingRule = await this.ruleService.getRule(name, searchQuery, rulesPackage);
      const response = {
        message: 'Rule data found successfully',
        existingRule
      };
      log('RULE_CREATE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_RULE_GET_BY_NAME', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // UPDATE
  @Patch('/:name/:rulesPackage?')
  async updateRule(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined,
    @Body() updatedRule: any
  ) {
    const reqInfo = this.getRequestData(req);

    log('RULE_UPDATE_REQUEST', { ...reqInfo, name });

    try {
      const existingRule = await this.ruleService.updateRule(name, rulesPackage, updatedRule);
      const response = {
        message: `Rule ${name} has been successfully updated`,
        existingRule
      };
      log('RULE_UPDATE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_RULE_UPDATE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // DELETE
  @Delete('/:name/:rulesPackage?')
  async deleteRule(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage?: string | undefined
  ) {
    const reqInfo = this.getRequestData(req);

    log('RULE_DELETE_REQUEST', { ...reqInfo, name });

    try {
      const deleteResponse = await this.ruleService.deleteRule(name, rulesPackage);
      const deleteResult = deleteResponse.result;

      const response = {
        message: `Rule ${name} has been deleted successfully with corresponding event and condition if it was in rulesPackage`,
        deleteResult
      };

      log('RULE_DELETE_RESPONSE', { ...reqInfo, response });

      return response;
    } catch (error) {
      log('ERROR_RULE_DELETE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Delete()
  async deleteAllRules(@Req() req: FastifyRequest, @Query() searchQuery: searchQueryNameDto) {
    const reqInfo = this.getRequestData(req);

    log('ALL_MATCHED_RULES_DELETE_REQUEST', { ...reqInfo });

    try {
      const deleteResponse = await this.ruleService.deleteAllRules(searchQuery);

      const response = {
        message: 'Request processed successfully for Delete All Rules',
        deleteResponse
      };
      log('ALL_MATCHED_RULES_DELETE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_RULES_DELETE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Delete('/deleteByPackageName/:rulesPackageName')
  async deleteRulesByPackageName(
    @Req() req: FastifyRequest,
    @Param('rulesPackageName') rulesPackageName: string
  ) {
    const reqInfo = this.getRequestData(req);

    log('RULES_DELETE_BY_PACKAGE_NAME_REQUEST', { ...reqInfo });

    try {
      const deletedRules = await this.ruleService.deleteRulesByPackageName(rulesPackageName);
      const response = {
        message: 'Request processed successfully for Delete By Package Name',
        deletedRules
      };

      log('RULES_DELETE_ALL_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_RULES_DELETE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Get('/remove-unique-name')
  async removeUniqueName(): Promise<ResponseInterface> {
    return await this.ruleService.removeUniqueName();
  }

  @Post('/:rules/:rulesPackage?')
  async postRules(
    @Req() req: FastifyRequest,
    @Param('rules') rules: 'all' | string,
    @Param('rulesPackage') rulesPackage: string | undefined,
    @Body() updateOptions: QueryOptions
  ) {
    if (!updateOptions) {
      throw CreateException({
        message: 'Body of the request cannot be empty',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
    const reqInfo = this.getRequestData(req);

    log('RULE_POST_REQUEST', { ...reqInfo, rules });
    try {
      const existingRule = await this.ruleService.postRules(
        updateOptions,
        rules,
        rulesPackage
      );
      const updateAllRules = rules === 'all';
      const messagePrefix = updateAllRules ? 'All rules' : `Rules ${rules}`;
      const message = `${messagePrefix} has been successfully posted`;
      const response = {
        message,
        existingRule
      };

      log('RULE_POST_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_RULE_POST', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Post('/drop-all-indexes')
  async dropAllIndexes(): Promise<ResponseInterface> {
    return await this.ruleService.dropAllIndexes();
  }

  @Get('/get-all-indexes')
  async getAllIndexes() {
    return await this.ruleService.getAllIndexes();
  }

  @Post('/create-all-indexes')
  async createAllIndexes() {
    return await this.ruleService.createAllIndexes();
  }

  @Get('/get-duplications')
  async getDuplications() {
    return await this.ruleService.getDuplications();
  }

  // READ
  @Post('/get-common-rules-for-services')
  async getCommonRulesForServices(
    @Req() req: FastifyRequest,
    @Body() servicesList: { servicesId: string[] },
    @Query() searchQuery: searchQueryNameDto
  ) {
    const reqInfo = this.getRequestData(req);

    log('GET_COMMON_RULES_FOR_SERVICES_REQUEST', { ...reqInfo });

    try {
      const commonRules = await this.ruleService.getCommonRulesForServices(
        servicesList.servicesId,
        searchQuery
      );

      return {
        message: `Common rules for services [${servicesList.servicesId}] selected successfully!`,
        commonRules
      };
    } catch (error) {
      log(
        'ERROR_GET_COMMON_RULES_FOR_SERVICES_REQUEST',
        { ...reqInfo, error },
        LogLevel.error
      );

      throw CreateException(error);
    }
  }

  @Post('/clone/:rulesPackage?')
  async cloneRule(
    @Req() req: FastifyRequest,
    @Param('rulesPackage') rulesPackage: string | undefined,
    @Body() body: cloneRuleDTO
  ) {
    const reqInfo = this.getRequestData(req);

    log('RULE_CLONE_REQUEST', { ...reqInfo, rulesPackage });

    try {
      const clonedRule = await this.ruleService.cloneRule(body, rulesPackage);
      const response = {
        message: `Rule has been cloned successfully`,
        clonedRule
      };
      log('RULE_CLONE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_RULE_CLONE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }
}
