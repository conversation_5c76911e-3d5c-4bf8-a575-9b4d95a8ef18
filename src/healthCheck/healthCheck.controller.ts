import {
  Body,
  Controller,
  Get,
  Headers,
  HttpStatus,
  Param,
  Post,
  Query
} from '@nestjs/common';
import { createResponse } from 'Helpers';
import { IReleaseVersionComparisonBody } from 'InterfacesAndTypes';
import { CreateException, log } from 'Utils';
import { HealthCheckService } from './healthCheck.service';
import { HcRawDebugDto, HcReleaseVersionBodyPipe } from './validators';

@Controller('hc')
export class HealthCheckController {
  constructor(private readonly healthCheckService: HealthCheckService) {}

  @Get('/rules/singlePackage/:name')
  async singleRulesPackageHealthCheck(
    @Param('name') name: string,
    @Query() query: HcRawDebugDto
  ) {
    try {
      const { debug, raw } = query;

      const response = await this.healthCheckService.getRulePackageChecksum(name, raw, debug);

      return createResponse(HttpStatus.OK, 'Single package rules checksum', response, raw);
    } catch (error) {
      throw CreateException(error);
    }
  }

  @Get('/rules/allPackages')
  async allRulesPackageHealthCheck(@Query() query: HcRawDebugDto) {
    try {
      const { debug, raw } = query;

      const response = await this.healthCheckService.getAllRulesPackagesChecksum(raw, debug);

      return createResponse(HttpStatus.OK, 'All packages rules checksum', response, raw);
    } catch (error) {
      throw CreateException(error);
    }
  }

  @Get('/rulesPackages/singleRulesPackageConfig/:name')
  async singlePackageConfig(@Param('name') name: string, @Query() query: HcRawDebugDto) {
    try {
      const { debug, raw } = query;

      const response = await this.healthCheckService.singlePackageConfig(name, raw, debug);

      return createResponse(
        HttpStatus.OK,
        'Single rulesPackage config checksum',
        response,
        raw
      );
    } catch (error) {
      throw CreateException(error);
    }
  }

  @Get('/rulesPackages/allRulesPackageConfig')
  async allPackagesConfig(@Query() query: HcRawDebugDto) {
    try {
      const { debug, raw } = query;

      const response = await this.healthCheckService.allPackagesConfig(raw, debug);

      return createResponse(HttpStatus.OK, 'All rulesPackage config checksum', response, raw);
    } catch (error) {
      throw CreateException(error);
    }
  }

  @Post('/generateHcInput')
  async generateHcInput(
    @Body() body: { urlList?: string[] },
    @Headers() headers: Record<string, string>
  ) {
    try {
      const urlList = body?.urlList || [];

      if (!urlList?.length) {
        throw CreateException({
          message: 'No urls provided in body param',
          statusCode: HttpStatus.BAD_REQUEST
        });
      }

      return await this.healthCheckService.generateHcInput(urlList, headers);
    } catch (error) {
      throw CreateException(error);
    }
  }

  /**
   * Compare release versions of display config in BE and FE
   * @param body - body with urlList and config
   * @param query - query with raw and debug
   * @returns response with compared versions
   */
  @Post('/releaseVersion')
  async releaseVersionComparison(
    @Body(HcReleaseVersionBodyPipe) body: IReleaseVersionComparisonBody,
    @Query() query: HcRawDebugDto
  ) {
    const { raw, debug } = query;

    if (debug) {
      log('RELEASE_VERSION_COMPARISON_DEBUG_NOT_IMPLEMENTED');
    }

    try {
      const fullResponse = await this.healthCheckService.releaseVersionComparison(body);

      const simplifiedResponse = fullResponse.reduce(
        (acc, r) => acc && r.fetchedAdMap && r.generatorResponded && r.versionsEqual,
        true
      );

      const result = query.raw ? fullResponse : simplifiedResponse;

      log('RELEASE_VERSION_COMPARISON_RESULT', { result });

      return createResponse(
        simplifiedResponse ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE,
        'Release version comparison',
        result,
        raw
      );
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }

  @Get('/serviceId/:serviceId?')
  async getServiceIdVersion(
    @Param('serviceId') serviceIdCommaSeparated: string | undefined,
    @Query() query: HcRawDebugDto
  ) {
    const { raw, debug } = query;

    try {
      const serviceIdList = serviceIdCommaSeparated ? serviceIdCommaSeparated.split(',') : [];
      const result = await this.healthCheckService.compareVersionsBetweenBases(
        debug,
        serviceIdList
      );
      return createResponse(
        HttpStatus.OK,
        'Lambda SDK and AdsLayouts Selected versions',
        result,
        raw
      );
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }
}
