import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  ExtractResult,
  GeneratorAdMapsResult,
  IComparableRulesType,
  IGeneratorResponsePartial,
  IReleaseVersionComparisonBody,
  ReleaseVersionComparisonResult,
  VersionComparisonResponse
} from 'InterfacesAndTypes';
import { log } from 'Utils';
import {
  DisplayConfigFiltersService,
  DisplayType,
  LogLevel,
  Rule,
  RuleDocument,
  RulesPackages,
  RulesPackagesDocument
} from 'ads-layouts-tools';
import sha256 from 'crypto-js/sha256';
import { Model } from 'mongoose';
import { DisplayConfigService } from '../displayConfig/displayConfig.service';
import { validators } from '../envalidConfig';

@Injectable()
export class HealthCheckService {
  constructor(
    @InjectModel(Rule.name) private ruleModel: Model<RuleDocument>,
    @InjectModel(RulesPackages.name) private rulesPackagesModel: Model<RulesPackagesDocument>,
    private readonly displayConfigFiltersService: DisplayConfigFiltersService,
    private readonly displayConfigService: DisplayConfigService
  ) {}

  private generateChecksum(data: unknown) {
    return sha256(JSON.stringify(data)).toString();
  }

  private groupArrayBy<T extends Record<K, PropertyKey>, K extends keyof T>(
    items: T[],
    key: K
  ): Record<T[K], T[]> {
    return items.reduce(
      (result, item) => ({
        ...result,
        [item[key]]: [...(result[item[key]] || []), item]
      }),
      {} as Record<T[K], T[]>
    );
  }

  private mapRulesToComparableObject(rules: RuleDocument[]): IComparableRulesType[] {
    return rules
      .map(rule => ({
        ruleName: rule?.name,
        rulesPackage: rule?.rulesPackage ?? 'original',
        conditionName: rule.conditions?.name,
        eventName: rule.event?.name
      }))
      .sort((a, b) => a.ruleName.localeCompare(b.ruleName));
  }

  async getRulePackageChecksum(
    packageName: string,
    raw: boolean,
    debug: boolean
  ): Promise<
    | string
    | {
        packageName: string;
        rulesChecksum: string;
        rules?: IComparableRulesType[];
      }
  > {
    const rules = await this.ruleModel
      .find({ rulesPackage: packageName })
      .populate({ path: 'event conditions' })
      .exec();

    const comparableRules = this.mapRulesToComparableObject(rules);

    const rulesChecksum = this.generateChecksum(comparableRules);

    const response = {
      packageName,
      rulesChecksum
    };

    return raw
      ? rulesChecksum
      : debug
        ? {
            ...response,
            rules: comparableRules
          }
        : response;
  }

  async getAllRulesPackagesChecksum(
    raw: boolean,
    debug: boolean
  ): Promise<
    | string
    | {
        packageName: string;
        rulesChecksum: string;
      }[]
    | {
        packageName: string;
        rulesChecksum: string;
        rules: IComparableRulesType[];
      }[]
  > {
    const rules = await this.ruleModel
      .find({ rulesPackage: { $exists: true } })
      .populate({ path: 'event conditions' })
      .exec();

    const comparableRules = this.mapRulesToComparableObject(rules);

    const groupedByPackage = this.groupArrayBy(comparableRules, 'rulesPackage');

    const names = Object.keys(groupedByPackage);

    const response = names.map(packageName => ({
      packageName,
      rulesChecksum: this.generateChecksum(groupedByPackage[packageName])
    }));

    return raw
      ? response.map(el => el.rulesChecksum).join(',')
      : debug
        ? response.map(el => ({ ...el, rules: groupedByPackage[el.packageName] }))
        : response;
  }

  async singlePackageConfig(
    rulesPackage: string,
    raw: boolean,
    debug: boolean
  ): Promise<
    | string
    | {
        rulesPackageName: string;
        rulesPackageConfigChecksum: string;
        rulesPackageConfig?: RulesPackagesDocument;
      }
  > {
    const rulesPackageConfig =
      (await this.rulesPackagesModel
        .findOne({ name: rulesPackage })
        .select('-createdAt -updatedAt')) ?? undefined;

    const rulesPackageConfigChecksum = this.generateChecksum(rulesPackageConfig);

    const response = {
      rulesPackageName: rulesPackage,
      rulesPackageConfigChecksum
    };

    return raw
      ? rulesPackageConfigChecksum
      : debug
        ? {
            ...response,
            rulesPackageConfig
          }
        : response;
  }

  async allPackagesConfig(
    raw: boolean,
    debug: boolean
  ): Promise<
    | string
    | {
        rulesPackageName: string;
        rulesPackageConfigChecksum: string;
        rulesPackageConfig?: RulesPackagesDocument[];
      }[]
  > {
    const allRulesPackageConfig = await this.rulesPackagesModel
      .find()
      .select('-createdAt -updatedAt');

    const groupedByPackage = this.groupArrayBy(allRulesPackageConfig, 'name');

    const names = Object.keys(groupedByPackage);

    const response = names.map(rulesPackageName => ({
      rulesPackageName,
      rulesPackageConfigChecksum: this.generateChecksum(groupedByPackage[rulesPackageName][0])
    }));

    return raw
      ? response.map(el => el.rulesPackageConfigChecksum).join(',')
      : debug
        ? response.map(el => ({
            ...el,
            rulesPackageConfig: groupedByPackage[el.rulesPackageName]
          }))
        : response;
  }

  async generateHcInput(urlList: string[], headers: Record<string, string>) {
    const results = await Promise.all(
      urlList.map(async singleUrl => {
        const admapUrl = new URL(singleUrl);
        admapUrl.searchParams.set('unicorn', 'admap');

        const admapResponse = await fetch(admapUrl);
        const admapBody = await admapResponse.json();

        const singleCurl = this.generateSingleCurl(singleUrl, headers, admapBody);

        return { admapUrl, singleCurl };
      })
    );

    return results;
  }

  generateSingleCurl(url: string, headers: Record<string, string>, data: object | string) {
    let curlCommand = "curl -L 'https://stage-at-adslayouts.tvn.pl/generator'";

    for (const [key, value] of Object.entries(headers)) {
      if (key.startsWith('x-at')) {
        curlCommand += ` -H '${key}: ${value}'`;
      }
    }

    curlCommand += " -H 'Content-Type: application/json'";

    if (data) {
      const formattedData = typeof data === 'object' ? JSON.stringify(data) : data;
      curlCommand += ` -d '${formattedData.replaceAll('"', "'")}'`;
    }

    return curlCommand;
  }

  /**
   * Generate array of BodyElements with admap data, fetched from unicorn-admap or with error information if fetching fails
   *
   * @param urlList - list of urls to fetch admap from
   * @returns - array of objects with admap data
   */
  generateAdMaps(urlList: string[]): Promise<GeneratorAdMapsResult[]> {
    return Promise.all(
      urlList.map(async url => {
        const admapUrl = new URL(url);
        admapUrl.searchParams.set('unicorn', 'admap');

        try {
          const admapResponse = await fetch(admapUrl);

          if (!admapResponse.ok) {
            throw Error(`Failed to fetch admap, status: ${admapResponse.statusText}`);
          }

          const admapBody = await admapResponse.json();

          return { fetchedAdMap: true, admapUrl, admapBody };
        } catch (error) {
          log('WARN_FETCHING_AD_MAP_FAILED', { admapUrl, error }, LogLevel.warn);

          const { message, stack } =
            error instanceof Error ? error : new Error('Unknown error');

          return { fetchedAdMap: false, admapUrl, error: { message, stack } };
        }
      })
    );
  }

  /**
   * Sends a POST request to the generator endpoint with the provided site-Map with type BodyElement.
   *
   * @param data - The data to be sent in the request body.
   * @returns - A promise resolving to the response of the fetch request.
   */
  buildGeneratorPromise(data: string | object): Promise<Response> {
    const location = validators.HC_RELEASE_VERSIONS_LOCATION_URL;
    const headers = { 'Content-Type': 'application/json' };
    const body = typeof data === 'object' ? JSON.stringify(data) : data;

    const awaitableRequest = fetch(location, { method: 'POST', headers, body });

    return awaitableRequest;
  }

  /**
   * Takes an array of generatorAdMaps and returns a promise resolving to an array of partial responses from AdMaps.
   * ExtractResults contains information about whether the generator was able to extract a release version
   * and the extracted data or information about the error that occurred.
   *
   * @param generatorAdMaps - An array of BodyElements with admap data, fetched from unicorn-admap or with error information if fetching fails
   * @returns A promise resolving to an array of ExtractResults
   */
  private extractGeneratorResult(
    generatorAdMaps: GeneratorAdMapsResult[]
  ): Promise<ExtractResult[]> {
    return Promise.all(
      generatorAdMaps.map(async (adMap): Promise<ExtractResult> => {
        const { fetchedAdMap, admapUrl } = adMap;

        if (!fetchedAdMap) {
          return adMap as ExtractResult; // TODO: refactor after strict:true
        }

        try {
          const response = await this.buildGeneratorPromise(adMap.admapBody);

          if (!response.ok) {
            throw Error(`Failed to fetch generator response, status: ${response.statusText}`);
          }

          const responseBody = (await response.json()) as IGeneratorResponsePartial;

          const {
            requestMeta: { serviceId, siteVersion, serviceEnv, time },
            version
          } = responseBody;

          return {
            fetchedAdMap: true,
            generatorResponded: true,
            admapUrl,
            serviceId,
            siteVersion,
            serviceEnv,
            time,
            version
          };
        } catch (error) {
          log('WARN_FETCHING_GENERATOR_RESPONSE', { admapUrl, error }, LogLevel.warn);

          const { message, stack } =
            error instanceof Error ? error : new Error('Unknown error');

          return {
            fetchedAdMap,
            generatorResponded: false,
            admapUrl,
            error: { message, stack }
          };
        }
      })
    );
  }

  /**
   * Compares the release versions from generator results with the backend display Configs.
   *
   * This function iterates over the given generator results and compares each release version
   * extracted from the generator with the corresponding backend release version obtained from
   * the display configurations. It logs any discrepancies between the versions and returns
   * an array of comparison results indicating whether the versions are equal.
   *
   * @param generatorResults - An array of results from the ad map generator containing release version information.
   * @param displayConfigs - An array of display configurations used to select the backend release version.
   * @returns A promise that resolves to an array of comparison results, each indicating whether the versions
   *          from the generator and backend are equal, along with additional comparison metadata.
   */

  private compareReleaseVersions(
    generatorResults: ExtractResult[],
    displayConfigs: DisplayType[]
  ): Promise<ReleaseVersionComparisonResult[]> {
    return Promise.all(
      generatorResults.map(async (result: ExtractResult) => {
        const { fetchedAdMap } = result;

        if (!fetchedAdMap || !result.generatorResponded) {
          return result as ReleaseVersionComparisonResult; // TODO: refactor after strict:true
        }

        const {
          admapUrl,
          serviceId,
          siteVersion = '',
          serviceEnv = '',
          time,
          version: versionWorker
        } = result;

        let versionBE: string | undefined;
        let versionsEqual = true;

        try {
          versionBE = await this.displayConfigFiltersService.selectReleaseVersion({
            serviceId,
            serviceEnv,
            siteVersion,
            time,
            displayConfigs
          });

          if (!versionBE) {
            log(
              'WARN_NO_RELEASE_VERSION_FOUND',
              { admapUrl, serviceId, siteVersion, serviceEnv, time },
              LogLevel.warn
            );
            versionsEqual = false;
          }
        } catch (error) {
          log(
            'WARN_SELECT_RELEASE_VERSION_FAILED',
            { admapUrl, serviceId, siteVersion, serviceEnv, time, error },
            LogLevel.warn
          );
          versionsEqual = false;
        }

        const versionBEClean = versionBE?.replace(/\/$/, '') ?? '';

        if (versionWorker !== versionBEClean) {
          log(
            `WARN_VERSIONS_ARE_NOT_EQUAL`,
            {
              versionWorker,
              versionBE: versionBEClean,
              admapUrl,
              serviceId,
              siteVersion,
              serviceEnv,
              time
            },
            LogLevel.warn
          );

          versionsEqual = false;
        }

        return {
          fetchedAdMap,
          generatorResponded: true,
          versionsEqual,
          versionWorker,
          versionBackend: versionBEClean,
          admapUrl
        };
      })
    );
  }

  /**
   * Compares release versions returned by admap generator with the versions in DisplayConfig.
   * @param body - An object with a urlList of admaps and a config identifier for DisplayConfig.
   * @returns A promise resolving to an array of ReleaseVersionComparisonResult.
   */
  async releaseVersionComparison(
    body: IReleaseVersionComparisonBody
  ): Promise<ReleaseVersionComparisonResult[]> {
    log('GET_DISPLAY_CONFIGS');

    const displayConfigData = await this.displayConfigFiltersService.getLambdaDisplayConfigs(
      body.config
    );
    const displayConfigs =
      await this.displayConfigFiltersService.parseSDKLambdaDisplayConfigOutput(
        displayConfigData
      );

    log('GENERATE_ADMAPS');
    const generatorAdMaps = await this.generateAdMaps(body.urlList);

    log('INVOKE_GENERATORS');
    const generatorResults = await this.extractGeneratorResult(generatorAdMaps);

    log('COMPARE_VERSIONS');
    const comparisons = await this.compareReleaseVersions(generatorResults, displayConfigs);

    return comparisons;
  }

  async compareVersionsBetweenBases(
    debug: boolean,
    serviceId: string[]
  ): Promise<VersionComparisonResponse | boolean> {
    try {
      const comparisonResult = serviceId.length
        ? await this.displayConfigService.compareLambdaAndDBSpecificVersion(serviceId)
        : await this.displayConfigService.compareLambdaAndDBVersions();

      return debug ? comparisonResult : comparisonResult.comparison;
    } catch (error) {
      log('ERROR_GET_SERVICE_ID_VERSION', { error, serviceId }, LogLevel.error);
      throw error;
    }
  }
}
