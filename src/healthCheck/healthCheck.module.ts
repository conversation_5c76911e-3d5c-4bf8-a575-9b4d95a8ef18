import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Conditions,
  ConditionsSchema,
  DisplayConfig,
  DisplayConfigFiltersModule,
  DisplayConfigSchema,
  Event,
  Rule,
  RuleSchema,
  RulesPackages,
  RulesPackagesSchema
} from 'ads-layouts-tools';
import { log } from 'Utils';
import { DisplayConfigModule } from '../displayConfig/displayConfig.module';
import { DisplayConfigService } from '../displayConfig/displayConfig.service';
import { EventSchema } from '../schema/dbIndices';
import { HealthCheckController } from './healthCheck.controller';
import { HealthCheckService } from './healthCheck.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Rule.name,
        schema: RuleSchema
      },
      {
        name: Event.name,
        schema: EventSchema
      },
      {
        name: Conditions.name,
        schema: ConditionsSchema
      },
      {
        name: RulesPackages.name,
        schema: RulesPackagesSchema
      },
      {
        name: DisplayConfig.name,
        schema: DisplayConfigSchema
      }
    ]),
    DisplayConfigModule,
    DisplayConfigFiltersModule.configure(log)
  ],
  controllers: [HealthCheckController],
  providers: [HealthCheckService, DisplayConfigService]
})
export class HealthCheckModule {}
