import { HttpStatus, PipeTransform } from '@nestjs/common';
import { IReleaseVersionComparisonBody } from 'InterfacesAndTypes';
import * as joi from 'joi';
import { CreateException } from 'Utils';
import { validators } from '../../envalidConfig';

export enum CDNType {
  PROD = 'PROD',
  RC = 'RC',
  STAGE = 'STAGE'
}

export enum ParamsMacro_AL {
  prefixCDN = '$PREFIX_CDN'
}

/**
 * HcReleaseVersionBodyPipe is responsible for validating and transforming the release version comparison body.
 * It ensures that the provided config matches one of the defined CDN types and that the URL list contains valid URLs.
 */
export class HcReleaseVersionBodyPipe implements PipeTransform<IReleaseVersionComparisonBody> {
  private readonly urlCDN = validators.HC_RELEASE_VERSIONS_CDN_URL;
  private readonly allowedHosts = validators.HC_RELEASE_VERSIONS_ALLOWED_HOSTS.split(';');

  private readonly cdnPrefixMap = new Map<CDNType, string>([
    [CDNType.PROD, ''],
    [CDNType.RC, 'rc-'],
    [CDNType.STAGE, 'stage-']
  ]);

  private cdnUrlFactory(cdn: CDNType = CDNType.PROD): string {
    const prefixCDN = this.cdnPrefixMap.get(cdn) ?? '';

    return this.urlCDN.replace(ParamsMacro_AL.prefixCDN, prefixCDN);
  }

  private readonly DisplayCDNConfigs = new Map<string, string>(
    Object.entries(CDNType).map(([key, value]) => [key, this.cdnUrlFactory(value)])
  );

  private readonly schema = joi.object<IReleaseVersionComparisonBody>({
    urlList: joi
      .array()
      .items(
        joi
          .string()
          .pattern(new RegExp(`^(${this.allowedHosts.join('|')})`))
          .uri()
      )
      .min(1)
      .required(),

    config: joi
      .string()
      .valid(...Object.values(CDNType))
      .required()
      .external((value: CDNType, helpers) => {
        const cdnConfig = this.DisplayCDNConfigs.get(value);
        if (!cdnConfig) {
          return helpers.error('any.invalid');
        }
        return cdnConfig;
      })
  });

  async transform(value: unknown): Promise<IReleaseVersionComparisonBody> {
    try {
      const result = await this.schema.validateAsync(value);
      return result;
    } catch (error) {
      throw CreateException({
        message: error.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }
}
