import { HttpStatus } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { ErrorInterface } from 'ads-layouts-tools';

class GetLoadErrorModel implements ErrorInterface {
  @ApiProperty({ example: HttpStatus.BAD_REQUEST })
  statusCode!: HttpStatus;

  @ApiProperty({
    example:
      'invalid json response body at https://rc-display-at.cdntvn.pl/sdk-display/configs/release/1.43.0/full.json reason: Unexpected token < in JSON at position 0'
  })
  message!: string;

  @ApiProperty({ example: '2024-01-17T09:12:14:509+01:00' })
  dateTime?: string;

  @ApiProperty({ example: '1801594875267668356' })
  traceId?: string;
}

class LoadCommonStatus {
  @ApiProperty({ example: 'release/1.39.0/' })
  release!: string;

  @ApiProperty({
    example: 'https://rc-display-at.cdntvn.pl/sdk-display/configs/release/1.39.0/full.json'
  })
  releaseUrl!: URL;
}

class LoadStatusSuccessful extends LoadCommonStatus {}

class LoadStatusFailed extends LoadCommonStatus {
  @ApiProperty({ example: 'fakty' })
  serviceId?: string;

  @ApiProperty()
  error?: GetLoadErrorModel;
}

class LoadStatus {
  @ApiProperty({ type: [LoadStatusSuccessful] })
  successfull?: LoadStatusSuccessful[];

  @ApiProperty({ type: [LoadStatusFailed] })
  failed?: LoadStatusFailed[];
}

export class GetLoadResponseModel {
  @ApiProperty({ example: '3.14 sec.' })
  processingTime?: string;

  @ApiProperty({ type: LoadStatus })
  loadStatus?: LoadStatus;
}
