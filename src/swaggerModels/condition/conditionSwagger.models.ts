import { HttpStatus } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { ErrorInterface } from 'ads-layouts-tools';
import { SimpleResponseModel } from 'SwaggerModels/swaggerCommonModels';

class SingleConditionValue {
  @ApiProperty({ example: 1 })
  count?: number;

  @ApiProperty({ example: 'narrow-inherited' })
  type?: string;
}

class SingleCondition {
  @ApiProperty({ example: 'column-narrow' })
  fact?: string;

  @ApiProperty({ example: 'containsOfType' })
  operator?: string;

  @ApiProperty({ type: SingleConditionValue })
  value?: SingleConditionValue;
}

export class PostConditionRequestModel {
  @ApiProperty({ example: 'prawaSzpaltaNotd' })
  name?: string;

  @ApiProperty({ type: [SingleCondition] })
  all?: SingleCondition[];
}

class PostConditionCreatedModel extends PostConditionRequestModel {
  @ApiProperty({ example: '2023-08-28T23:04:52.166Z' })
  createdAt?: string;

  @ApiProperty({ example: '2023-08-28T23:04:52.166Z' })
  updatedAt?: string;
}

export class PostConditionResponseModel {
  @ApiProperty({ example: HttpStatus.OK })
  statusCode?: HttpStatus;

  @ApiProperty({ type: [PostConditionCreatedModel] })
  createdConditionDetails?: PostConditionCreatedModel;
}

export class ConditionErrorModel implements ErrorInterface {
  @ApiProperty({ example: HttpStatus.BAD_REQUEST })
  statusCode!: HttpStatus;

  @ApiProperty({
    example:
      'E11000 duplicate key error collection: adsLayouts.conditions index: name_1 dup key: { name: "prawaSzpaltaNotd" }'
  })
  message!: string;

  @ApiProperty({ example: '2024-01-17T09:12:14:509+01:00' })
  dateTime?: string;

  @ApiProperty({ example: '1801594875267668356' })
  traceId?: string;
}

export class GetConditionRequestModel {
  @ApiProperty({ example: 'above second teaser' })
  name?: string;
}

export class GetConditionResponseModel {
  @ApiProperty({ example: 'All conditions data found successfully' })
  message?: string;

  @ApiProperty({ type: [PostConditionCreatedModel] })
  conditionsData?: PostConditionCreatedModel;
}

export class GetConditionByNameResponseModel {
  @ApiProperty({ example: 'Condition data found successfully' })
  message?: string;

  @ApiProperty({ type: PostConditionCreatedModel })
  existingCondition?: PostConditionCreatedModel;
}

export class DeleteConditionByNameResponseModel extends SimpleResponseModel {
  @ApiProperty({
    example: 'Condition prawaSzpaltaNotd has been deleted successfully'
  })
  declare message: string;

  @ApiProperty({ example: '200' })
  status?: string;

  @ApiProperty({ type: PostConditionCreatedModel })
  deletedCondition?: PostConditionCreatedModel;
}
