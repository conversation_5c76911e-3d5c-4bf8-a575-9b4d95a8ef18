import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { searchQueryNameDto } from 'Helpers';
import { log } from 'Utils';
import { LogLevel } from 'ads-layouts-tools';
import { DeleteResult } from 'mongodb';
import {
  createNewRulesPackageDto,
  createPackageFromServiceDto
} from './dto/rulesPackages.dto';
import { RulesPackagesService } from './rulesPackages.service';

@Controller('rulesPackages')
export class RulesPackagesController {
  constructor(private readonly rulesPackagesService: RulesPackagesService) {}

  @Post('/:rulesPackageToCopy?')
  async createRulesPackage(
    @Param('rulesPackageToCopy') rulesPackageToCopy: string,
    @Body() newRulesPackage: createNewRulesPackageDto
  ) {
    try {
      let rulesPackage;

      if (rulesPackageToCopy) {
        rulesPackage = await this.rulesPackagesService.createRulesPackageCopy(
          rulesPackageToCopy,
          newRulesPackage?.name
        );
      } else {
        rulesPackage = await this.rulesPackagesService.createRulesPackage(newRulesPackage);
      }

      const response = {
        status: 'Rules Package created successfully',
        createdRulesPackageDetails: rulesPackage
      };

      log('RULES_PACKAGES_CREATE_RESPONSE', { response });

      return response;
    } catch (error) {
      log('ERROR_RULES_PACKAGES_CREATE', { error }, LogLevel.error);

      throw error;
      // throw UpdateException(error, { statusCode: HttpStatus.CONFLICT }); // example
    }
  }

  @Post('/createPackageFromService/:serviceId')
  async createRulesPackageFromServiceRules(
    @Param('serviceId') serviceId: string,
    @Body() createObject: createPackageFromServiceDto
  ) {
    try {
      const rulesPackage = await this.rulesPackagesService.createRulesPackageFromServiceRules(
        serviceId,
        createObject
      );

      const response = {
        status: `Rules Package from service: ${serviceId} created successfully`,
        createdRulesPackageDetails: rulesPackage
      };

      log('RULES_PACKAGES_FROM_SERVICE_CREATE_RESPONSE', { response });

      return response;
    } catch (error) {
      log('ERROR_RULES_PACKAGES_FROM_SERVICE_CREATE', { error }, LogLevel.error);

      throw error;
    }
  }

  @Get('/')
  async getAllRulesPackages(@Query() searchQuery: searchQueryNameDto) {
    try {
      const response = await this.rulesPackagesService.getAllRulesPackages(searchQuery);

      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('/:name')
  async getSingleRulesPackage(@Param('name') name: string) {
    try {
      const rulesFromPackage = await this.rulesPackagesService.getRulesFromPackageByName(name);
      const currentPackage = await this.rulesPackagesService.getSingleRulesPackage(name);

      const response = {
        status: `Rules Package with name: ${name} get successfully`,
        getRulesPackageDetails: {
          rulesPackage: name,
          rules: rulesFromPackage,
          version: currentPackage?.version,
          freeze: currentPackage?.freeze
        }
      };

      return response;
    } catch (error) {
      throw error;
    }
  }

  @Patch('/:name')
  async updateRulesPackage(
    @Body() rulesPackageUpdate: createNewRulesPackageDto,
    @Param('name') name: string
  ) {
    try {
      const rulesPackage = await this.rulesPackagesService.updateRulesPackage(
        name,
        rulesPackageUpdate
      );

      const response = {
        status: `Rules Package ${name} updated successfully`,
        updatedRulesPackageDetails: rulesPackage
      };

      return response;
    } catch (error) {
      log('ERROR_RULES_PACKAGES_UPDATE', { error }, LogLevel.error);

      throw error;
    }
  }

  @Delete('/:name')
  async deleteRulesPackage(@Param('name') name: string) {
    try {
      const deletedRulesPackage = await this.rulesPackagesService.deleteRulesPackage(name);

      const response = {
        status: 'Rules Package deleted successfully',
        deletedRulesPackageDetails: deletedRulesPackage
      };

      log('RULES_PACKAGES_DELETED_RESPONSE', { response });
      return response;
    } catch (error) {
      log('ERROR_RULES_PACKAGES_DELETE', { error: error.response }, LogLevel.error);
      throw error;
    }
  }

  @Delete('/')
  async deleteFoundRulesPackage(@Query() searchQuery: searchQueryNameDto): Promise<{
    status: string;
    deletedRulesPackagesDetails: DeleteResult;
  }> {
    try {
      const deleteResults =
        await this.rulesPackagesService.deleteFoundRulesPackage(searchQuery);

      const response = {
        status: 'Rules Package deleted successfully',
        deletedRulesPackagesDetails: deleteResults
      };

      log('RULES_PACKAGES_DELETED_RESPONSE', { response });
      return response;
    } catch (error) {
      log('ERROR_RULES_PACKAGES_DELETE', { error: error.response }, LogLevel.error);
      throw error;
    }
  }

  @Post('/:name/freeze/on')
  async freezeOnRulesPackage(@Param('name') name: string) {
    try {
      const response = await this.rulesPackagesService.freezeRulesPackage(name, true);

      return response;
    } catch (error) {
      throw error;
    }
  }

  @Post('/:name/freeze/off')
  async freezeOffRulesPackage(@Param('name') name: string) {
    try {
      const response = await this.rulesPackagesService.freezeRulesPackage(name, false);

      return response;
    } catch (error) {
      throw error;
    }
  }
}
