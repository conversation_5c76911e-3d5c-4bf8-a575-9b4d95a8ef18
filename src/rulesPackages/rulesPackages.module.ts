import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { RulesPackages, RulesPackagesSchema } from 'ads-layouts-tools';
import { RulesModule } from '../rules/rules.module';
import { ServiceToPackageMapModule } from '../serviceToPackageMap/serviceToPackageMap.module';
import { RulesPackagesController } from './rulesPackages.controller';
import { RulesPackagesService } from './rulesPackages.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: RulesPackages.name, schema: RulesPackagesSchema }]),
    forwardRef(() => RulesModule),
    ServiceToPackageMapModule
  ],
  controllers: [RulesPackagesController],
  providers: [RulesPackagesService],
  exports: [RulesPackagesService]
})
export class RulesPackagesModule {}
