import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  LogLevel,
  ResponseInterface,
  ResponseType,
  Rule,
  RulesPackages,
  RulesPackagesDocument,
  SearchQueryName
} from 'ads-layouts-tools';
import {
  calculateNextVersion,
  getArrayDifference,
  getLikeQueryOptions,
  useFreezeChecker
} from 'Helpers';
import { IRulesPackageFreezeCheckerResponse } from 'InterfacesAndTypes';
import { DeleteResult } from 'mongodb';
import { Model, ModifyResult } from 'mongoose';
import { CreateException, log } from 'Utils';
import { RuleService } from '../rules/rules.service';
import { ServiceToPackageMapService } from '../serviceToPackageMap/serviceToPackageMap.service';
import {
  createNewRulesPackageDto,
  createPackageFromServiceDto
} from './dto/rulesPackages.dto';

@Injectable()
export class RulesPackagesService {
  constructor(
    private readonly ruleService: RuleService,
    private readonly serviceToPackageMapService: ServiceToPackageMapService,
    @InjectModel(RulesPackages.name)
    private rulesPackagesModel: Model<RulesPackagesDocument>
  ) {}

  async createRulesPackage(
    singleRulesPackage: createNewRulesPackageDto,
    packageNameCopyFrom?: string
  ): Promise<RulesPackages> {
    if (packageNameCopyFrom) {
      await this.getSingleRulesPackage(packageNameCopyFrom);
    }

    await this.checkRulesExistingCondition(singleRulesPackage, packageNameCopyFrom);

    const rulesInPackage = await this.ruleService.getRulesForPackage(singleRulesPackage.name);
    const existedRulesFromCreationInPackage = rulesInPackage.filter(rule =>
      singleRulesPackage.rules?.includes(rule.name)
    );

    if (existedRulesFromCreationInPackage.length) {
      const dupList = existedRulesFromCreationInPackage
        .map(dup => `${dup.name} - ${dup.rulesPackage}`)
        .join(' | ');
      const errorInfo = `Rules name and rulesPackage pair already exist: ${dupList}. Stop create rulesPackage.`;

      log('RULES_PACKAGES_CREATE_ABORT', { error: errorInfo }, LogLevel.error);

      throw CreateException({
        message: errorInfo,
        statusCode: HttpStatus.CONFLICT
      });
    }

    const rulesPackageVersion = calculateNextVersion();

    const newRulesPackage = new this.rulesPackagesModel({
      name: singleRulesPackage.name,
      version: rulesPackageVersion
    });

    await newRulesPackage.save();

    const copyResult: ResponseInterface = await this.ruleService.copyRulesForSpecificPackage(
      singleRulesPackage.name,
      singleRulesPackage.rules,
      packageNameCopyFrom
    );

    if (copyResult.type === ResponseType.ERROR) {
      await newRulesPackage.deleteOne();
      throw CreateException({
        message: copyResult.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    return { ...newRulesPackage.toJSON(), rules: singleRulesPackage.rules };
  }

  async checkRulesExistingCondition(
    singleRulesPackage: createNewRulesPackageDto,
    packageNameCopyFrom?: string
  ) {
    if (!singleRulesPackage.rules?.length) {
      const errorInfo = `Rules list to create/update package '${singleRulesPackage.name}' ${packageNameCopyFrom ? "as copy of '" + packageNameCopyFrom + "'" : ''} is empty. Stop create/update rulesPackage.`;

      log('RULES_PACKAGES_CREATE_ABORT', { error: errorInfo }, LogLevel.error);
      throw CreateException({
        message: errorInfo,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const missingRulesFromList: string[] = await this.ruleService.getMissingRulesFromList(
      singleRulesPackage.rules
    );

    if (missingRulesFromList.length) {
      const errorInfo = `Rules [${missingRulesFromList}] is missing in ${packageNameCopyFrom || 'original'} space so it's not possible to create its copies. Stop create rulesPackage.`;

      log('RULES_PACKAGES_CREATE_ABORT', { error: errorInfo }, LogLevel.error);
      throw CreateException({
        message: errorInfo,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }

  async createRulesPackageCopy(rulesPackageToCopy: string, newPackageName: string) {
    if (!newPackageName) {
      const match = rulesPackageToCopy.match(/_ver(\d+)$/);

      if (match) {
        const newVersion = parseInt(match[1], 10) + 1;

        newPackageName = rulesPackageToCopy.replace(/_ver\d+$/, `_ver${newVersion}`);
      } else {
        newPackageName = rulesPackageToCopy + '_ver1';
      }
    }

    const rulesNamesInPackageToCopy = await this.getRulesFromPackageByName(rulesPackageToCopy);

    return this.createRulesPackage(
      {
        name: newPackageName,
        rules: rulesNamesInPackageToCopy?.map(rule => rule.name)
      },
      rulesPackageToCopy
    );
  }

  async createRulesPackageFromServiceRules(
    serviceId: string,
    createObject: createPackageFromServiceDto
  ): Promise<RulesPackages> {
    const searchQuery: SearchQueryName = {
      serviceId,
      rulesPackage: 'original'
    };

    const rulesForService = await this.ruleService.getAllRules(searchQuery, false);

    const rulesNameList = rulesForService.map(rule => rule.name);

    return this.createRulesPackage({
      name: createObject.name,
      rules: rulesNameList
    });
  }

  async getSingleRulesPackage(name: string): Promise<RulesPackages> {
    const singleRulesPackage = await this.rulesPackagesModel.findOne({ name });

    if (!singleRulesPackage) {
      throw CreateException({
        message: `Rules package ${name} not found!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return singleRulesPackage;
  }

  async getAllRulesPackages(
    searchQuery: SearchQueryName
  ): Promise<RulesPackages[] | string[]> {
    const returnOnlyNames = searchQuery.onlyNames;
    const options = getLikeQueryOptions(['name'], searchQuery);
    const allRulesPackages = await this.rulesPackagesModel
      .find(options)
      .select('-_id -__v')
      .lean();

    if (!allRulesPackages || allRulesPackages.length == 0) {
      log('ERROR_GET_ALL_RULES_PACKAGES', { allRulesPackages }, LogLevel.error);
      throw CreateException({
        message: 'Rules packages not found!',
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    if (returnOnlyNames) {
      return allRulesPackages.map(rulesPackage => rulesPackage.name);
    }
    const packagesWithRules = await Promise.all(
      allRulesPackages.map(async rulesPackage => {
        rulesPackage.rules = (await this.getRulesFromPackageByName(rulesPackage.name))?.map(
          rule => rule.name
        );

        return rulesPackage;
      })
    );

    return packagesWithRules;
  }

  async getRulesFromPackageByName(name: string): Promise<Rule[]> {
    await this.getSingleRulesPackage(name);

    const rulesFromPackage = await this.ruleService.getRulesForPackage(name);

    if (!rulesFromPackage) {
      log('ERROR_GET_RULES_FROM_PACKAGE_BY_NAME', { name }, LogLevel.error);
      throw CreateException({
        message: `Any rules for package with name: ${name} not found!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return rulesFromPackage;
  }

  async updateRulesPackage(
    packageName: string,
    rulesPackageUpdate: createNewRulesPackageDto
  ): Promise<RulesPackages> {
    await this.getSingleRulesPackage(packageName);

    if ((await this.checkIsRulesPackageFreeze(packageName)).isFreeze) {
      throw CreateException({
        message: `Operation is not allowed because rules package ${packageName} is frozen!`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    await this.checkRulesExistingCondition(rulesPackageUpdate, packageName);

    /*
      1. reguły z tabeli Rules, które były przed zmianą przypisanej do rulesPackage, a teraz wyleciały muszą zostać usunięte (update: wraz z odpowiadającymi im eventami i conditionami)
      2. reguły, których nie było, a które doszły po update muszą zostać dodane do Rules (skopiowane z odpowiednim rulesPackage, update: równiez eventy i conditiony)
    */

    const currentRulesFromPackage = (await this.getRulesFromPackageByName(packageName))?.map(
      rule => rule.name
    );

    const rulesToCopy = getArrayDifference(rulesPackageUpdate.rules, currentRulesFromPackage);
    const rulesToRemove = getArrayDifference(
      currentRulesFromPackage,
      rulesPackageUpdate.rules
    );

    let hasChanges = false;

    if (rulesToCopy.length > 0) {
      const copiedRulesResult: ResponseInterface =
        await this.ruleService.copyRulesForSpecificPackage(packageName, rulesToCopy);

      if (copiedRulesResult.type === ResponseType.ERROR) {
        log(
          'ERROR_RULES_PACKAGES_UPDATE',
          { error: copiedRulesResult.message },
          LogLevel.error
        );

        throw CreateException({
          message: copiedRulesResult.message,
          statusCode: HttpStatus.BAD_REQUEST
        });
      }
      hasChanges = true;
    }

    if (rulesToRemove.length > 0) {
      const deletedRulesResult: ResponseInterface =
        await this.ruleService.deleteRulesWithSpecificPackage(packageName, rulesToRemove);

      if (deletedRulesResult.type === ResponseType.ERROR) {
        log(
          'ERROR_RULES_PACKAGES_UPDATE',
          { error: deletedRulesResult.message },
          LogLevel.error
        );

        throw CreateException({
          message: deletedRulesResult.message,
          statusCode: HttpStatus.BAD_REQUEST
        });
      }
      hasChanges = true;
    }

    if (!hasChanges) {
      log(
        'RULES_PACKAGES_UPDATE_INFO',
        { message: 'No rules were changed after update rulesPackage.' },
        LogLevel.info
      );

      throw CreateException({
        message: 'No rules were changed after update rulesPackage.',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const updatedRulesPackage = await this.rulesPackageVersionUp(packageName);

    rulesPackageUpdate.version = updatedRulesPackage?.version;

    log('RULES_PACKAGES_UPDATE_RESPONSE', {
      packageName,
      rulesAdded: rulesToCopy,
      rulesRemoved: rulesToRemove,
      rulesPackageUpdate
    });

    return rulesPackageUpdate;
  }

  @useFreezeChecker({ key: 0, isSearchQuery: false })
  async deleteRulesPackage(packageName: string): Promise<ModifyResult<RulesPackages>> {
    const mappedServices =
      await this.serviceToPackageMapService.getMappedServices(packageName);

    if (!!mappedServices) {
      throw CreateException({
        message: `Rules packages with name: ${packageName} is assigned to services [${mappedServices}] and can't be deleted!`,
        statusCode: HttpStatus.METHOD_NOT_ALLOWED
      });
    }

    const deletedRulesPackage = await this.rulesPackagesModel
      .findOneAndDelete({ name: packageName })
      .exec();

    if (!deletedRulesPackage) {
      throw CreateException({
        message: `Rules package with name: ${packageName} not found and not deleted!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    try {
      await this.ruleService.deleteRulesByPackageName(packageName);
    } catch (error) {
      let options = undefined;
      if (error instanceof HttpException) {
        options = { cause: error };
      }
      throw CreateException({
        message: `Rules from package ${packageName} cant't be deleted after successfully Rules package deletion.`,
        statusCode: HttpStatus.NOT_FOUND,
        options
      });
    }

    return deletedRulesPackage;
  }

  @useFreezeChecker({ key: 0, isSearchQuery: true })
  async deleteFoundRulesPackage(searchQuery: SearchQueryName): Promise<DeleteResult> {
    const options = getLikeQueryOptions(['name'], searchQuery, true);
    const deleteResults = await this.rulesPackagesModel.deleteMany(options);

    if (!deleteResults.deletedCount) {
      throw CreateException({
        message: `Rules packages with name like '${searchQuery.name}' not found and not deleted!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return deleteResults;
  }

  async getRulesPackageCurrentVersion(rulesPackageName: string): Promise<string | undefined> {
    const currentPackage = await this.getSingleRulesPackage(rulesPackageName);

    return currentPackage.version;
  }

  async rulesPackageVersionUp(rulesPackageName: string): Promise<RulesPackages> {
    const currentVersion = await this.getRulesPackageCurrentVersion(rulesPackageName);
    const rulesPackageNextVersion = calculateNextVersion(currentVersion);

    const updatedRulesPackage = await this.rulesPackagesModel.findOneAndUpdate(
      { name: rulesPackageName },
      { version: rulesPackageNextVersion },
      { new: true }
    );

    if (!updatedRulesPackage) {
      throw CreateException({
        message: `Rules package with name: ${rulesPackageName} not found and not updated!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    log('RULES_PACKAGE_VERSION_UP', {
      rulesPackageName,
      newVersion: rulesPackageNextVersion,
      updatedRulesPackage
    });

    return updatedRulesPackage;
  }

  async freezeRulesPackage(
    rulesPackageName: string,
    freezeState: boolean
  ): Promise<RulesPackages> {
    const updatedRulesPackage = await this.rulesPackagesModel.findOneAndUpdate(
      { name: rulesPackageName },
      { freeze: freezeState },
      { new: true }
    );

    if (!updatedRulesPackage) {
      throw CreateException({
        message: `Rules package with name: ${rulesPackageName} not found and not updated!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    log('RULES_PACKAGE_FREEZE_STATE_UPDATE', {
      rulesPackageName,
      freezeState: freezeState.toString(),
      updatedRulesPackage
    });

    return updatedRulesPackage;
  }

  async checkIsRulesPackageFreeze(
    rulesPackageName: string
  ): Promise<IRulesPackageFreezeCheckerResponse> {
    const options = getLikeQueryOptions(
      ['name'],
      { name: rulesPackageName, onlyNames: false },
      true
    );

    const rulesPackagesFreezeStates = await this.rulesPackagesModel
      .find(options)
      .select('name freeze -_id');

    const freezeInfo: string = rulesPackagesFreezeStates
      .reduce(
        (acc, curr) =>
          `${acc} ${curr.name} - ${curr.freeze ? 'is freeze' : 'is not freeze'} | `,
        ''
      )
      .replace(/\s\|\s$/, '');

    log('RULES_PACKAGES_FREEZE_STATES', { freezeInfo });

    return {
      isFreeze: rulesPackagesFreezeStates.some(rulesPackage => rulesPackage.freeze),
      freezeInfo
    };
  }
}
