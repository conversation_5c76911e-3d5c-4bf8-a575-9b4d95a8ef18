import * as Jo<PERSON> from 'joi';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';

@JoiSchemaOptions({
  allowUnknown: false,
  cache: true
})
export class AdConfigUnusedVersionInputDto {
  @JoiSchema(Joi.string().required())
  currentVersions!: string;

  @JoiSchema(Joi.boolean().optional())
  forceDelete?: boolean;

  @JoiSchema(Joi.boolean().optional())
  forceDeleteLastAdded?: boolean;
}
