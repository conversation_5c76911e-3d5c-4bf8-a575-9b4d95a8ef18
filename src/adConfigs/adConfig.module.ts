import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdConfigService } from './adConfig.service';
import { AdConfigController } from './adConfig.controller';
import { AdConfig, AdConfigSchema } from 'ads-layouts-tools';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: AdConfig.name,
        schema: AdConfigSchema
      }
    ])
  ],
  controllers: [AdConfigController],
  providers: [AdConfigService],
  exports: [AdConfigService]
})
export class AdConfigModule {}
