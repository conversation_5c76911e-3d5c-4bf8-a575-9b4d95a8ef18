import { Controller, Delete, Get, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LogLevel } from 'ads-layouts-tools';
import { formatPerformanceTime } from 'Helpers';
import { DeleteResult } from 'mongodb';
import { CreateException, log } from 'Utils';
import { AdConfigService } from './adConfig.service';
import { AdConfigInputReleaseVersionsDto } from './dto';
import { SetupAdConfigsDto } from './dto/setupAdConfigs.dto';
import {
  AdConfigReleaseVersionAmount,
  AdConfigUnusedVersionInputDto,
  AdConfigUnusedVersionOutputDto
} from './schema';
import { GetLoadDoc } from './swagger/adConfig.doc.decorator';

@Controller('adConfig')
@ApiTags('AdConfig')
export class AdConfigController {
  constructor(private readonly adConfigService: AdConfigService) {}

  @Get('/stats')
  @GetLoadDoc()
  async countConfigReleaseVersions(
    @Query() queryParams: AdConfigInputReleaseVersionsDto
  ): Promise<AdConfigReleaseVersionAmount[]> {
    const { releaseVersions } = queryParams;
    const response = await this.adConfigService.countAdConfigReleaseVersions(releaseVersions);
    log('COUNT_RELEASE_VERSIONS_RESPONSE', { response });
    return response;
  }

  @Get('/load')
  @GetLoadDoc()
  async setupAdConfigs(@Query() queryParams: SetupAdConfigsDto) {
    const getCurrentPerformanceTime = () => Date.now();
    const startProcessingTimer = getCurrentPerformanceTime();

    const response = await this.adConfigService.setupAdConfigs(queryParams);

    return {
      processingTime: formatPerformanceTime(startProcessingTimer, getCurrentPerformanceTime()),
      ...response
    };
  }

  @Get('/count')
  @GetLoadDoc()
  async countAdConfigs() {
    try {
      const response = await this.adConfigService.countAdConfigs();

      return response;
    } catch (error) {
      log('ERROR_CANNOT_COUNT_AD_CONFIGS', { error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Delete('/deleteUnusedVersions')
  async deleteUnusedVersions(
    @Query() queryParams: AdConfigUnusedVersionInputDto
  ): Promise<AdConfigUnusedVersionOutputDto> {
    const { currentVersions, forceDelete, forceDeleteLastAdded } = queryParams;

    try {
      const currentVersionsArray = currentVersions.split(',');
      const response = await this.adConfigService.deleteUnusedVersions(currentVersionsArray, {
        forceDelete,
        forceDeleteLastAdded
      });

      if (response === 0) {
        return {
          status: `No configs required to delete`
        };
      }

      if (response > 0) {
        return {
          status: `Successfully deleted ${response} configs`
        };
      }

      return {
        status: `Cannot delete any configs`,
        response
      };
    } catch (error) {
      log('ERROR_CANNOT_DELETE_PASSED_UNUSED_VERSIONS', { error }, LogLevel.error);
      throw error;
    }
  }

  @Delete('/deleteAll')
  async resetAllAdConfigs(): Promise<
    { status: string } | { status: string; response: DeleteResult }
  > {
    try {
      const response = await this.adConfigService.deleteAllAdConfigs();
      const { deletedCount } = response;

      if (deletedCount) {
        return {
          status: `Successfully deleted ${response.deletedCount} configs`
        };
      }

      return {
        status: `Cannot delete any configs`,
        response
      };
    } catch (error) {
      log('ERROR_CANNOT_DELETE_ALL_AD_CONFIGS', { error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Post('/drop-all-indexes')
  async dropAllIndexes() {
    return await this.adConfigService.dropAllIndexes();
  }
}
