import { BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { AdConfig, AdConfigDocument, LogLevel, ResponseInterface } from 'ads-layouts-tools';
import { createResponse } from 'Helpers';
import {
  AdditionalData,
  AuditData,
  ConfigData,
  ConfigLogData,
  FetchedConfigs,
  FetchType,
  LogResponseType,
  result,
  ResultingLoad,
  ResultType
} from 'InterfacesAndTypes';
import { DeleteResult } from 'mongodb';
import { Model } from 'mongoose';
import fetch from 'node-fetch';
import { CreateException, log } from 'Utils';
import { validators } from '../envalidConfig';
import { SetupAdConfigsQueryParams } from './dto/setupAdConfigs.dto';
import { AdConfigReleaseVersionAmount } from './schema';

@Injectable()
export class AdConfigService {
  constructor(
    @InjectModel(AdConfig.name)
    private adConfigModel: Model<AdConfigDocument>
  ) {}

  async deleteAllAdConfigs(): Promise<DeleteResult> {
    const deleted = await this.adConfigModel.deleteMany();
    log('DELETED_ALL_AD_CONFIGS');

    return deleted;
  }

  async countAdConfigs() {
    const count = await this.adConfigModel.countDocuments();

    return count;
  }

  async setupAdConfigs(params: SetupAdConfigsQueryParams): Promise<LogResponseType> {
    const { v, chunk } = params;

    log('AD_CONFIG_LOAD_PARAMS', { ...params });

    // configsLoadStatus
    const successfulFetch: FetchType[] = [];
    const failedFetch: FetchType[] = [];

    const fullJsonUrl = new URL(validators.FULL_JSON_URL);
    let releaseUrl: URL;

    try {
      releaseUrl = new URL(`${v}full.json`, fullJsonUrl);
    } catch (error) {
      log(
        'ADCONFIG_LOAD_INVALID_RELEASE_URL_ERROR',
        { v, fullJsonUrl, error },
        LogLevel.error
      );

      throw CreateException({
        statusCode: HttpStatus.BAD_REQUEST,
        message: 'Invalid release URL for load AdConfigs'
      });
    }

    if (fullJsonUrl.hostname !== releaseUrl.hostname) {
      throw CreateException({
        statusCode: HttpStatus.BAD_REQUEST,
        message: `Release URL '${releaseUrl.hostname}' does not match the expected hostname '${fullJsonUrl.hostname}'`
      });
    }

    const releaseName = v;

    try {
      const fetchReleaseData = await fetch(releaseUrl);
      const fetchHttpStatus = fetchReleaseData.status;

      const logData: FetchType = {
        fetchHttpStatus,
        releaseUrl,
        releaseName
      };

      if (fetchHttpStatus === 200) {
        successfulFetch.push(logData);
        log('SUCCESSFULLY_LOADED_FULL_JSON_CONFIG', logData);
      } else {
        failedFetch.push(logData);
        log('WARN_FAILED_LOAD_FULL_JSON_CONFIG', logData, LogLevel.warn);
        return {
          successfulFetch,
          failedFetch,
          configs: null
        };
      }

      const releaseFull = (await fetchReleaseData.json()) as FetchedConfigs;
      const { configs, auditData } = releaseFull;

      const adsLayoutsAdditionalData: AdditionalData = { releaseUrl, releaseName };

      let insertedCount = 0;
      let updatedCount = 0;
      let skippedCount = 0;
      let failedCount = 0;

      for (const config of configs) {
        const configSrc = config.src;

        const configChunks: ConfigData[][] = [];
        for (let i = 0; i < config.data.length; i += chunk) {
          configChunks.push(config.data.slice(i, i + chunk));
        }

        for (const currentChunk of configChunks) {
          const results = await this.processConfigChunk(
            currentChunk,
            configSrc,
            adsLayoutsAdditionalData,
            auditData,
            params
          );

          insertedCount += results.inserted.length;
          updatedCount += results.updated.length;
          skippedCount += results.skipped.length;
          failedCount += results.failed.length;
        }
      }

      return {
        successfulFetch,
        failedFetch,
        configs: { insertedCount, updatedCount, skippedCount, failedCount }
      };
    } catch (err) {
      log('ERROR_CANNOT_LOAD_AD_CONFIGS', { releaseUrl, releaseName, err }, LogLevel.error);
      throw CreateException(
        {
          message: 'Failed to load or process ad configs',
          statusCode: HttpStatus.BAD_REQUEST
        },
        { cause: err }
      );
    }
  }

  private async processConfigChunk(
    chunk: ConfigData[],
    configSrc: string,
    adsLayoutsAdditionalData: AdditionalData,
    auditData: AuditData,
    options: SetupAdConfigsQueryParams
  ): Promise<ResultingLoad> {
    const results: ResultingLoad = {
      inserted: [],
      updated: [],
      skipped: [],
      failed: []
    };

    await Promise.all(
      chunk.map(async configData => {
        try {
          const resultTemp = await this.processConfigItem(
            configData,
            configSrc,
            adsLayoutsAdditionalData,
            auditData,
            options
          );

          if (resultTemp.status === ResultType.failed) {
            results.failed.push({
              configLogData: resultTemp.configLogData,
              error: resultTemp.error
            });
          } else {
            results[resultTemp.status].push(resultTemp.configLogData);
          }
        } catch (error) {
          log('WARN_PROCESSING_CONFIG_ITEM_FAILED', { configData, error }, LogLevel.warn);
        }
      })
    );

    return results;
  }

  private async processConfigItem(
    configData: ConfigData,
    configSrc: string,
    adsLayoutsAdditionalData: AdditionalData,
    auditData: AuditData,
    options: SetupAdConfigsQueryParams
  ): Promise<result> {
    const { v, forceUpdate, insertOnly } = options;
    const configName = configData.config_name;
    const configServiceId = configData.serviceId;
    const releaseServices = configServiceId.map(item => item.replaceAll('_', ''));

    const configLogData: any = {
      configName,
      configSrc,
      configServiceId: configServiceId.join(','),
      ...adsLayoutsAdditionalData,
      releaseServices
    } as ConfigLogData;

    const currentConfigSearchData = {
      'adsLayoutsAdditionalData.releaseName': v,
      serviceId: { $in: configServiceId },
      config_name: configName
    };

    try {
      const currentConfigExists = await this.adConfigModel.exists(currentConfigSearchData);

      if (insertOnly && currentConfigExists) {
        log('SKIPPED_INSERT_CONFIG_EXISTS', configLogData);
        return { configLogData, status: ResultType.skipped };
      }

      const insetOrUpdateData: AdConfig = {
        adsLayoutsAdditionalData: {
          ...adsLayoutsAdditionalData,
          releaseServices
        },
        auditData,
        src: configSrc,
        ...configData
      };

      if (currentConfigExists && !insertOnly) {
        log('HAS_CURRENT_CONFIG', configLogData);
        if (!forceUpdate) {
          const shouldUpdateStatus = await this.adConfigModel.exists({
            ...currentConfigSearchData,
            'auditData.modifiedDate': { $ne: auditData.modifiedDate }
          });

          if (!shouldUpdateStatus) {
            log('SKIPPED_UPDATE_SAME_MODIFIED_DATE', configLogData);
            return { configLogData, status: ResultType.skipped };
          }
        }

        log('UPDATE_START', configLogData);
        const updateStatus = await this.adConfigModel.updateOne(
          currentConfigSearchData,
          { $set: insetOrUpdateData },
          { new: true }
        );

        if (updateStatus.modifiedCount > 0) {
          log('SUCCESSFULLY_UPDATED_EXISTING_CONFIG', configLogData);
          return { configLogData, status: ResultType.updated };
        }

        log('SKIPPED_UPDATE_NO_CHANGE', configLogData);
        return { configLogData, status: ResultType.skipped };
      } else {
        const insert = new this.adConfigModel(insetOrUpdateData);
        await insert.save();

        log('SUCCESSFULLY_INSERTED_NEW_CONFIG', configLogData);
        return { configLogData, status: ResultType.inserted };
      }
    } catch (error) {
      log('ERROR_PROCESSING_CONFIG_ITEM', { ...configLogData, error }, LogLevel.error);
      return { configLogData, status: ResultType.failed, error };
    }
  }

  async deleteUnusedVersions(
    currentVersions: string[],
    forceFlags: { forceDelete?: boolean; forceDeleteLastAdded?: boolean }
  ): Promise<number> {
    log('ABOUT_TO_DELETE_UNUSED_ADCONFIGS', { currentVersions });

    const configsToRemoveSearch = {
      'adsLayoutsAdditionalData.releaseName': { $nin: currentVersions }
    };
    const configsToDelete = await this.adConfigModel.find(configsToRemoveSearch);
    if (configsToDelete.length < currentVersions.length) {
      throw CreateException({
        message: 'You are trying to delete nonexisting version',
        statusCode: HttpStatus.METHOD_NOT_ALLOWED
      });
    }

    await this.validateConfigsBeforeDelete(configsToDelete, forceFlags);

    const logDeleteData = configsToDelete.map(c => ({
      configName: c.config_name,
      configSrc: c.src,
      configServiceId: c.serviceId.join(','),
      releaseUrl: c.adsLayoutsAdditionalData.releaseUrl,
      releaseName: c.adsLayoutsAdditionalData.releaseName
    }));

    logDeleteData.forEach(c => {
      log('DELETED_AD_CONFIG', c);
    });

    const deleteStatus = await this.adConfigModel.deleteMany(configsToRemoveSearch);

    log('DELETED_AD_CONFIGS_COUNT', {
      deletedCount: deleteStatus.deletedCount.toString()
    });
    return logDeleteData.length;
  }

  private async validateConfigsBeforeDelete(
    configsToDelete: any[],
    forceFlags: { forceDelete?: boolean; forceDeleteLastAdded?: boolean }
  ): Promise<void> {
    const today = Date.now();
    const expirationDate = new Date(today - validators.DAYS_EXPIRATION_INTERVAL);

    const protectedConfigs = configsToDelete.filter(adConfigItem => {
      return adConfigItem.createdAt > expirationDate;
    });

    if (
      !(forceFlags.forceDelete || forceFlags.forceDeleteLastAdded) &&
      protectedConfigs.length > 0
    ) {
      const protectedConfigReleaseNames = protectedConfigs.reduce((acc, item) => {
        const value = item.adsLayoutsAdditionalData.releaseName;
        if (!acc.includes(value)) acc.push(value);
        return acc;
      }, []);

      const prettifiedOutput = protectedConfigReleaseNames.join(', ');

      throw CreateException({
        message: `At least one config cannot be deleted because is too new. Protected Config Versions: [${prettifiedOutput}]`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const totalConfigs = await this.adConfigModel.countDocuments();
    const deletionPercentage = (configsToDelete.length / totalConfigs) * 100;
    const maxDeletionPercentage = validators.MAX_DELETION_PERCENTAGE;

    if (!forceFlags.forceDelete && deletionPercentage > maxDeletionPercentage) {
      throw CreateException({
        message: `Deletion percentage exceeds ${maxDeletionPercentage}%`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }

  async countAdConfigReleaseVersions(
    releaseVersion?: string
  ): Promise<AdConfigReleaseVersionAmount[]> {
    const searchedReleaseVersionsArray = releaseVersion?.split(',') || [];

    const countingQuery = {
      $group: { _id: '$auditData.releaseVersion', count: { $sum: 1 } }
    };
    const filteringQuery = {
      $match: {
        'auditData.releaseVersion': { $in: searchedReleaseVersionsArray }
      }
    };
    const queryDB = searchedReleaseVersionsArray.length
      ? [filteringQuery, countingQuery]
      : [countingQuery];
    let selectedAdConfigReleaseVersions = await this.adConfigModel.aggregate(queryDB);

    selectedAdConfigReleaseVersions = selectedAdConfigReleaseVersions.map(
      (item: { _id: string; count: number }) => {
        return {
          version: item._id,
          count: item.count
        };
      }
    );

    const releaseVersionsFoundInDB = new Set(
      selectedAdConfigReleaseVersions.map(({ version }) => version)
    );

    searchedReleaseVersionsArray.forEach(item => {
      !releaseVersionsFoundInDB.has(item) &&
        selectedAdConfigReleaseVersions.push({ version: item, count: 0 });
    });

    return selectedAdConfigReleaseVersions;
  }

  async dropAllIndexes(): Promise<ResponseInterface> {
    try {
      await this.adConfigModel.collection.dropIndexes();

      return createResponse(
        HttpStatus.OK,
        'All indexes from adConfig collection removed successfully!'
      );
    } catch (err) {
      throw new BadRequestException(err?.message);
    }
  }
}
