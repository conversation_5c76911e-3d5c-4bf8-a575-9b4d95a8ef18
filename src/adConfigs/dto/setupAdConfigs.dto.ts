import * as Jo<PERSON> from 'joi';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';

export interface SetupAdConfigsQueryParams {
  v: string;
  forceUpdate?: boolean;
  insertOnly?: boolean;
  chunk: number;
}

@JoiSchemaOptions({
  allowUnknown: false,
  abortEarly: false
})
export class SetupAdConfigsDto implements SetupAdConfigsQueryParams {
  @JoiSchema(Joi.string().min(1).required())
  v!: string;

  @JoiSchema(Joi.boolean().default(false).optional())
  forceUpdate!: boolean;

  @JoiSchema(Joi.boolean().default(false).optional())
  insertOnly!: boolean;

  @JoiSchema(Joi.number().integer().min(1).default(1).optional())
  chunk!: number;
}
