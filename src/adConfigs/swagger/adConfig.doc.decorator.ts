import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiProduces } from '@nestjs/swagger';
import { GetLoadResponseModel } from 'SwaggerModels/adConfig/adConfigSwagger.models';

export function GetLoadDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: GetLoadResponseModel
    }),
    ApiResponse({ status: 400, description: 'Bad request' }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}
