import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { AdConfig, AdConfigSchema } from 'ads-layouts-tools';
import { FetchedConfigs, LoadResult, LogResponseType } from 'InterfacesAndTypes';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import * as fetch from 'node-fetch';
import {
  adConfigDefaultFactory,
  AdConfigDefaultParameters,
  adConfigFactory,
  DEFAULT_RELEASE_NAME,
  fetchedConfigsDefaultFactory
} from 'Utils';
import { AdConfigService } from './adConfig.service';

jest.mock('Utils/logger');
jest.mock('node-fetch');

const SETUP_1 = 'SETUP_1' as const;
const SETUP_2 = 'SETUP_2' as const;
const SETUP_3 = 'SETUP_3' as const;
const SETUP_4 = 'SETUP_4' as const;

const masterIds = [SETUP_1, SETUP_2, SETUP_3, SETUP_4];

const AdConfigMocks = masterIds.map(masterId => adConfigDefaultFactory(masterId));

describe('AdConfig Service test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let service: AdConfigService;
  let mongoAdConfigModel: Model<AdConfig>;
  let fetchSpy: jest.SpyInstance;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoAdConfigModel = mongoConnection.model(AdConfig.name, AdConfigSchema);

    const app: TestingModule = await Test.createTestingModule({
      providers: [
        AdConfigService,
        {
          provide: getModelToken(AdConfig.name),
          useValue: mongoAdConfigModel
        }
      ]
    }).compile();

    service = app.get(AdConfigService);
  });

  beforeEach(async () => {
    await mongoAdConfigModel.insertMany(AdConfigMocks);
    fetchSpy = jest.spyOn(fetch, 'default');
  });

  afterEach(async () => {
    await mongoAdConfigModel.deleteMany({});
    jest.restoreAllMocks();
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  const mockFetchResponse = (status: number, data?: FetchedConfigs): Promise<Response> =>
    Promise.resolve(new Response(JSON.stringify(data), { status }));

  describe('setupAdConfigs', () => {
    describe('Successful scenarios', () => {
      it('should successfully fetch and insert new configs', async () => {
        const v = 'release/1.2.3/';
        const mockValidDefaultFetchedConfigs: FetchedConfigs = fetchedConfigsDefaultFactory([
          adConfigDefaultFactory('Mock_1', v)
        ]);

        fetchSpy.mockResolvedValueOnce(mockFetchResponse(200, mockValidDefaultFetchedConfigs));

        const result = await service.setupAdConfigs({
          v,
          forceUpdate: false,
          insertOnly: false,
          chunk: 10
        });

        expect(result).toEqual<LogResponseType>({
          successfulFetch: expect.arrayContaining([
            expect.objectContaining({
              fetchHttpStatus: 200,
              releaseName: v
            })
          ]),
          failedFetch: [],
          configs: { insertedCount: 1, updatedCount: 0, skippedCount: 0, failedCount: 0 }
        });

        const savedConfigs = await mongoAdConfigModel.find().lean();
        expect(savedConfigs).toHaveLength(AdConfigMocks.length + 1);
        expect(savedConfigs[savedConfigs.length - 1].config.masterId).toEqual('Mock_1');
      });

      it('should update existing config when forceUpdate is true', async () => {
        const updatedConfig: FetchedConfigs = fetchedConfigsDefaultFactory([
          adConfigFactory({ ...AdConfigDefaultParameters(SETUP_1), pageId: ['updated_page'] })
        ]);

        fetchSpy.mockResolvedValueOnce(mockFetchResponse(200, updatedConfig));

        const result = await service.setupAdConfigs({
          v: DEFAULT_RELEASE_NAME,
          forceUpdate: true,
          insertOnly: false,
          chunk: 10
        });

        expect(result.configs).toEqual<LoadResult>({
          insertedCount: 0,
          updatedCount: 1,
          skippedCount: 0,
          failedCount: 0
        });

        const updatedDbConfig = await mongoAdConfigModel
          .findOne({ 'config.masterId': SETUP_1 })
          .lean();
        expect(updatedDbConfig?.pageId).toStrictEqual(['updated_page']);
      });

      it('should not update existing config when insertOnly is true', async () => {
        const mockValidDefaultFetchedConfigs: FetchedConfigs = fetchedConfigsDefaultFactory([
          adConfigDefaultFactory(SETUP_1)
        ]);

        fetchSpy.mockResolvedValueOnce(mockFetchResponse(200, mockValidDefaultFetchedConfigs));

        const result = await service.setupAdConfigs({
          v: DEFAULT_RELEASE_NAME,
          forceUpdate: false,
          insertOnly: true,
          chunk: 10
        });

        expect(result.configs).toEqual<LoadResult>({
          insertedCount: 0,
          updatedCount: 0,
          skippedCount: 1,
          failedCount: 0
        });

        const dbConfig = (await mongoAdConfigModel.find({}).lean()).length;
        expect(dbConfig).toStrictEqual(AdConfigMocks.length);
      });

      it('should skip updating config when forceUpdate is true and insertOnly is true', async () => {
        const mockValidDefaultFetchedConfigs: FetchedConfigs = fetchedConfigsDefaultFactory([
          adConfigDefaultFactory(SETUP_1)
        ]);

        fetchSpy.mockResolvedValueOnce(mockFetchResponse(200, mockValidDefaultFetchedConfigs));

        const result = await service.setupAdConfigs({
          v: DEFAULT_RELEASE_NAME,
          forceUpdate: true,
          insertOnly: true,
          chunk: 10
        });

        expect(result.configs).toEqual<LoadResult>({
          insertedCount: 0,
          updatedCount: 0,
          skippedCount: 1,
          failedCount: 0
        });

        const dbConfig = (await mongoAdConfigModel.find({}).lean()).length;
        expect(dbConfig).toStrictEqual(AdConfigMocks.length);
      });

      it('should handle empty configs array', async () => {
        const emptyConfig: FetchedConfigs = fetchedConfigsDefaultFactory([]);

        fetchSpy.mockResolvedValueOnce(mockFetchResponse(200, emptyConfig));

        const result = await service.setupAdConfigs({
          v: DEFAULT_RELEASE_NAME,
          forceUpdate: false,
          insertOnly: false,
          chunk: 10
        });

        expect(result.configs?.insertedCount).toBe(0);
      });
    });

    describe('Failed scenarios', () => {
      it('should handle failed fetch', async () => {
        const v = 'non-existent';
        fetchSpy.mockResolvedValueOnce(mockFetchResponse(404));

        const result = await service.setupAdConfigs({
          v,
          forceUpdate: false,
          insertOnly: false,
          chunk: 10
        });

        expect(result).toEqual({
          successfulFetch: [],
          failedFetch: [expect.objectContaining({ fetchHttpStatus: 404, releaseName: v })],
          configs: null
        });

        const configs = await mongoAdConfigModel.find().lean();
        expect(configs).toHaveLength(AdConfigMocks.length);
      });

      it('should handle fetch error', async () => {
        fetchSpy.mockRejectedValueOnce(new Error('Network error'));

        await expect(
          service.setupAdConfigs({
            v: 'v1',
            forceUpdate: false,
            insertOnly: false,
            chunk: 10
          })
        ).rejects.toThrow();

        const configs = await mongoAdConfigModel.find().lean();
        expect(configs).toHaveLength(AdConfigMocks.length);
      });
    });
  });
});
