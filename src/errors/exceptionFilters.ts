import {
  ArgumentsHost,
  BadRequestException,
  Catch,
  ExceptionFilter,
  ForbiddenException,
  HttpException,
  HttpStatus,
  NotFoundException
} from '@nestjs/common';
import { ErrorInterface } from 'ads-layouts-tools';
import { MongoError } from 'mongodb';
import { Error as ValidatorError } from 'mongoose';
import { CreateException } from 'Utils';

@Catch(MongoError)
export class MongoExceptionFilter implements ExceptionFilter {
  catch(exception: MongoError, host: ArgumentsHost) {
    const { message, response: { statusCode } = {} as Partial<ErrorInterface> } =
      exception as any;

    throw CreateException({ message, statusCode });
  }
}

@Catch(BadRequestException, NotFoundException, ForbiddenException)
export class HttpExceptionFilter implements ExceptionFilter {
  catch(exception: HttpException) {
    const { message, statusCode } = exception.getResponse() as Partial<ErrorInterface>;

    if (message && statusCode) {
      throw CreateException({ message, statusCode });
    }

    throw CreateException({
      message: message ?? 'Unknown error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR
    });
  }
}

@Catch(ValidatorError)
export class MongooseValidatorExceptionFilter implements ExceptionFilter {
  catch(exception: ValidatorError) {
    const { message, response: { statusCode = 422 } = {} } = exception as any;

    throw CreateException({ message, statusCode });
  }
}
