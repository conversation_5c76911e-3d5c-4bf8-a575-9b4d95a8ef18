import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { VariantWeightsConfigService } from './variantWeightsConfig.service';
import { VariantWeightsConfig, VariantWeightsConfigSchema } from 'ads-layouts-tools';
import { VariantWeightsConfigController } from './variantWeightsConfig.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: VariantWeightsConfig.name,
        schema: VariantWeightsConfigSchema
      }
    ])
  ],
  controllers: [VariantWeightsConfigController],
  providers: [VariantWeightsConfigService]
})
export class VariantWeightsConfigModule {}
