import { Body, Controller, Delete, Get, HttpStatus, Patch, Post, Req } from '@nestjs/common';
import { VariantWeightsConfig, WeightsConfig } from 'ads-layouts-tools';
import { FastifyRequest } from 'fastify';
import { getRequestData } from 'Helpers';
import { DeleteResult } from 'mongodb';
import { CreateException, log } from 'Utils';
import { VariantWeightsConfigBodyValidatorPipe } from './schema/variantWeightsConfig.pipe';
import { VariantWeightsConfigService } from './variantWeightsConfig.service';

@Controller('variantWeightsConfig')
export class VariantWeightsConfigController {
  constructor(private readonly variantWeightsConfigService: VariantWeightsConfigService) {}

  @Post()
  async post(
    @Req() req: FastifyRequest,
    @Body(VariantWeightsConfigBodyValidatorPipe) body: WeightsConfig
  ): Promise<VariantWeightsConfig> {
    const reqInfo = getRequestData(req);

    log('VARIANT_WEIGHTS_CONFIG_CREATE_REQUEST', { ...reqInfo });

    const result = await this.variantWeightsConfigService.post(body);

    log('VARIANT_WEIGHTS_CONFIG_ADDED', { addedConfig: result });

    return result;
  }

  @Get()
  async get(@Req() req: FastifyRequest): Promise<VariantWeightsConfig> {
    const reqInfo = getRequestData(req);

    log('VARIANT_WEIGHTS_CONFIG_GET_REQUEST', { ...reqInfo });

    const config = await this.variantWeightsConfigService.get();

    if (!config) {
      throw CreateException({
        message: "Config doesn't exist",
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    log('VARIANT_WEIGHTS_CONFIG_GET_RESPONSE', { config });

    return config;
  }

  @Patch()
  async patch(
    @Req() req: FastifyRequest,
    @Body(VariantWeightsConfigBodyValidatorPipe) body: WeightsConfig
  ): Promise<VariantWeightsConfig> {
    const reqInfo = getRequestData(req);

    log('VARIANT_WEIGHTS_CONFIG_UPDATE_REQUEST', { ...reqInfo });

    const result = await this.variantWeightsConfigService.patch(body);

    log('VARIANT_WEIGHTS_CONFIG_UPDATE_RESPONSE', { updateResult: result });

    return result;
  }

  @Delete()
  async delete(@Req() req: FastifyRequest): Promise<DeleteResult> {
    const reqInfo = getRequestData(req);

    log('VARIANT_WEIGHTS_CONFIG_DELETE_REQUEST', { ...reqInfo });

    const result = await this.variantWeightsConfigService.delete();

    log('VARIANT_WEIGHTS_CONFIG_DELETED_RESPONSE', { deleteResult: result });

    return result;
  }
}
