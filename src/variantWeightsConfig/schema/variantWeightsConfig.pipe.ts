import { HttpStatus, PipeTransform } from '@nestjs/common';
import { CreateException } from 'Utils';
import { AdConfigDeviceTypeEnum, WeightsConfig } from 'ads-layouts-tools';
import joi from 'joi';
import { JoiSchemaOptions } from 'nestjs-joi';

const weightValidator = joi.number().integer().min(0);

const layoutValidator = joi.object().pattern(joi.string(), weightValidator).optional();
const modulesValidator = joi
  .object()
  .pattern(joi.string(), joi.alternatives().try(weightValidator, layoutValidator))
  .optional();

const bodyValidator = joi
  .object<WeightsConfig>({
    [AdConfigDeviceTypeEnum.DESKTOP]: modulesValidator,
    [AdConfigDeviceTypeEnum.TABLET]: modulesValidator,
    [AdConfigDeviceTypeEnum.SMARTPHONE]: modulesValidator
  })
  .required()
  .custom((weightsConfig: WeightsConfig, helpers) => {
    const devices = Object.values(weightsConfig);

    const hasAtLeastOneWeight = devices.some(moduleType =>
      Object.values(moduleType).some(layout => {
        return (
          layout !== undefined &&
          (typeof layout === 'number' ||
            Object.values(layout as object).some(Number.isInteger))
        );
      })
    );

    if (!hasAtLeastOneWeight) {
      return helpers.message({
        custom: 'At least one weight must be specified correctly'
      });
    }

    return weightsConfig;
  }, 'Weights validation');

@JoiSchemaOptions({})
export class VariantWeightsConfigBodyValidatorPipe implements PipeTransform<WeightsConfig> {
  transform(body: object): WeightsConfig {
    const result = bodyValidator
      .options({ abortEarly: false, allowUnknown: false })
      .validate(body);

    if (result.error) {
      throw CreateException({
        message: `Invalid body: ${result.error.message}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    return body as WeightsConfig;
  }
}
