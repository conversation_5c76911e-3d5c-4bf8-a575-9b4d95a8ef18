import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Injectable
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  LogLevel,
  VariantWeightsConfig,
  VariantWeightsConfigDocument,
  WeightsConfig
} from 'ads-layouts-tools';
import dayjs from 'dayjs';
import { DeleteResult } from 'mongodb';
import { Model } from 'mongoose';
import { log } from 'Utils';

/**
 * Service for managing variant weights configurations.
 *
 * This service provides basic CRUD methods for managing
 * variant weights configurations stored in a MongoDB collection.
 */
@Injectable()
export class VariantWeightsConfigService {
  constructor(
    @InjectModel(VariantWeightsConfig.name)
    private variantWeightsConfigModel: Model<VariantWeightsConfigDocument>
  ) {}

  /**
   * Add a new variant weights configuration.
   *
   * If a variant weights configuration already exists, this method will throw a
   * ForbiddenException. Otherwise, it will create a new configuration with the given
   * weights and return it.
   * @param weights the weights for each device, module, and layout
   */
  async post(weights: WeightsConfig): Promise<VariantWeightsConfig> {
    await this.ensureOnlyOneConfig();

    log('ADD_VARIANT_WEIGHTS_CONFIG_INVOKED', { weights });
    try {
      const timestamp = Date.now();
      const formattedDate = dayjs(timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ');
      const newConfig: VariantWeightsConfig = { timestamp, formattedDate, weights };
      const insert = new this.variantWeightsConfigModel(newConfig);

      await insert.save();

      return newConfig;
    } catch (err) {
      log('ERROR_ADD_VARIANT_WEIGHTS_CONFIG', { err }, LogLevel.error);
      throw new BadRequestException(err?.message);
    }
  }

  /**
   * Retrieve the current variant weights configuration.
   *
   * This method returns the current variant weights configuration.
   * If no configuration exists, it returns null.
   * @returns the current variant weights configuration, or null if none exists
   */
  async get(): Promise<VariantWeightsConfig | null> {
    log('GET_VARIANT_WEIGHTS_CONFIG_INVOKED');

    const variantWeightsConfigs: VariantWeightsConfig | null =
      await this.variantWeightsConfigModel
        .findOne({})
        .select('-createdAt -updatedAt -_id -__v')
        .exec();

    if (variantWeightsConfigs === null) {
      log('VARIANT_WEIGHTS_CONFIG_NOT_FOUND', undefined, LogLevel.warn);
    }

    return variantWeightsConfigs;
  }

  /**
   * Updates the current variant weights configuration.
   *
   * This method updates the current variant weights configuration.
   * If no configuration exists, it throws a ForbiddenException.
   *
   * @param weights the variant weights configuration to update
   * @returns the updated variant weights configuration
   * @throws ForbiddenException if the variant weights configuration doesn't exist
   * @throws BadRequestException if there is an error while updating the configuration
   */
  async patch(weights: WeightsConfig): Promise<VariantWeightsConfig> {
    log('UPDATE_VARIANT_WEIGHTS_CONFIG_INVOKED', { weights });
    try {
      const timestamp = Date.now();
      const formattedDate = dayjs(timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ');

      const newConfig: VariantWeightsConfig = { timestamp, formattedDate, weights };

      const result = await this.variantWeightsConfigModel
        .findOneAndUpdate({}, { $set: newConfig })
        .select('-createdAt -updatedAt -_id -__v')
        .exec();

      if (result === null) {
        throw new ForbiddenException(
          `Variant weights config doesn't exist, it has to be created first`
        );
      }

      return newConfig;
    } catch (err) {
      if (err instanceof HttpException)
        log('ERROR_UPDATE_VARIANT_WEIGHTS_CONFIG', { err: err.message }, LogLevel.error);

      throw new BadRequestException(err?.message);
    }
  }

  /**
   * Deletes the variant weights configuration.
   *
   * @returns the result of the deletion, which includes the number of documents deleted and acknowledgement
   */
  delete(): Promise<DeleteResult> {
    log('DELETE_VARIANT_WEIGHTS_CONFIG_INVOKED');

    return this.variantWeightsConfigModel.deleteOne({}).exec();
  }

  private async ensureOnlyOneConfig(): Promise<void> {
    const existingConfig = await this.variantWeightsConfigModel.findOne({}).exec();

    if (existingConfig !== null) {
      throw new ForbiddenException(`Can't add variant weights config, one already exists`);
    }
  }
}
