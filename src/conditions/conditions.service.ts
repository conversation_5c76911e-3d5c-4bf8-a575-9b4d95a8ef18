import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  AllConditions,
  AllConditionsDocument,
  AnyConditions,
  AnyConditionsDocument,
  Conditions,
  ConditionsDocument,
  ConditionsKindNamesEnum,
  Rule,
  RuleDocument,
  SearchQueryName
} from 'ads-layouts-tools';
import { getFetchRuleOptions, getLikeQueryOptions, useF<PERSON>ze<PERSON>he<PERSON> } from 'Helpers';
import { DeleteResult } from 'mongodb';
import { Model, ModifyResult } from 'mongoose';
import { CreateException } from 'Utils';
import { RulesPackagesService } from '../rulesPackages/rulesPackages.service';

@Injectable()
export class ConditionService {
  constructor(
    private readonly rulesPackagesService: RulesPackagesService,
    @InjectModel(Conditions.name) private conditionModel: Model<ConditionsDocument>,
    @InjectModel(AllConditions.name) private allConditionsModel: Model<AllConditionsDocument>,
    @InjectModel(AnyConditions.name) private anyConditionsModel: Model<AnyConditionsDocument>,
    @InjectModel(Rule.name) private ruleModel: Model<RuleDocument>
  ) {}

  /**
   * Validate that the incoming condition does not contain both `all` and `any`
   * simultaneously and that at least one of them is present.
   */
  private validateConditionKind(condition: Conditions): void {
    const hasAll = 'all' in condition;
    const hasAny = 'any' in condition;

    if (hasAll && hasAny) {
      throw CreateException({
        message: 'Cannot use both "all" and "any" conditions in the same condition',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    if (!hasAll && !hasAny) {
      throw CreateException({
        message: 'Cannot use neither "all" nor "any" conditions in the same condition',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }

  private getConditionKind(condition: Conditions): ConditionsKindNamesEnum {
    return 'all' in condition ? ConditionsKindNamesEnum.All : ConditionsKindNamesEnum.Any;
  }

  private getModelByKind(
    kind: ConditionsKindNamesEnum
  ): Model<AllConditionsDocument> | Model<AnyConditionsDocument> {
    return kind === ConditionsKindNamesEnum.All
      ? this.allConditionsModel
      : this.anyConditionsModel;
  }

  // CREATE
  async createCondition(
    condition: AllConditions | AnyConditions
  ): Promise<ConditionsDocument> {
    const { name, rulesPackage } = condition;

    try {
      this.validateConditionKind(condition);
      const kind = this.getConditionKind(condition);

      const modelToUse = this.getModelByKind(kind);

      const conditionDocument = new modelToUse({ ...condition, kind });
      await conditionDocument.save();

      return conditionDocument;
    } catch (err) {
      if (err.code === 11000) {
        throw CreateException({
          message: `Condition ${name} for rulesPackage ${rulesPackage || 'original'} already exists`,
          statusCode: HttpStatus.CONFLICT
        });
      }

      if (err instanceof HttpException) {
        throw err;
      }

      throw CreateException({
        message: err?.message,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }

  // READ
  async getAllConditions(
    searchQuery: SearchQueryName
  ): Promise<ConditionsDocument[] | string[]> {
    const returnOnlyNames = searchQuery.onlyNames;
    const options = getLikeQueryOptions(['name', 'rulesPackage'], searchQuery);
    const conditionData = await this.conditionModel.find(options);

    if (!conditionData || conditionData.length == 0) {
      throw CreateException({
        message: 'Conditions data not found!',
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    if (returnOnlyNames) {
      return conditionData.map(condition => condition.name);
    }

    return conditionData;
  }

  async getCondition(
    name: string,
    rulesPackage: string | undefined
  ): Promise<ConditionsDocument> {
    const options = getFetchRuleOptions(name, rulesPackage);
    const existingCondition = await this.conditionModel.findOne(options).exec();

    if (!existingCondition) {
      throw CreateException({
        message: `Condition ${name} for rulesPackage ${rulesPackage || 'original'} not found`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return existingCondition;
  }

  // UPDATE
  @useFreezeChecker({ key: 1, isSearchQuery: false })
  async updateCondition(
    conditionName: string,
    rulesPackage: string | undefined,
    updatedCondition: Partial<AllConditions> | Partial<AnyConditions>
  ): Promise<ConditionsDocument> {
    this.validateConditionKind(updatedCondition as Conditions);
    const kind = this.getConditionKind(updatedCondition as Conditions);

    const options = getFetchRuleOptions(conditionName, rulesPackage);

    let existingCondition: ConditionsDocument | null = null;
    try {
      if (kind === ConditionsKindNamesEnum.All) {
        existingCondition = await this.allConditionsModel
          .findOneAndUpdate(
            options,
            { ...updatedCondition, kind },
            { new: true, runValidators: true }
          )
          .exec();
      } else {
        existingCondition = await this.anyConditionsModel
          .findOneAndUpdate(
            options,
            { ...updatedCondition, kind },
            { new: true, runValidators: true }
          )
          .exec();
      }
    } catch (err) {
      if (err.name == 'CastError') {
        throw CreateException({
          message: `Condition ${conditionName} for rulesPackage ${rulesPackage || 'original'} not updated. Reason: validation failed. ${err.message}`,
          statusCode: HttpStatus.BAD_REQUEST
        });
      }
    }

    if (!existingCondition) {
      throw CreateException({
        message: `Condition ${conditionName} for rulesPackage ${rulesPackage || 'original'} not updated`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    return existingCondition;
  }

  // DELETE
  @useFreezeChecker({ key: 1, isSearchQuery: false })
  async deleteCondition(
    name: string,
    rulesPackage: string | undefined
  ): Promise<ModifyResult<ConditionsDocument>> {
    const options = getFetchRuleOptions(name, rulesPackage);

    const rulesWithAssignedCondition =
      (await this.getRulesWithAssignedCondition(name, rulesPackage))?.map(
        rule => `${rule.name} (${rule.rulesPackage || 'original'})`
      ) || [];

    if (rulesWithAssignedCondition.length) {
      throw CreateException({
        message: `Condition ${name} for rulesPackage ${rulesPackage || 'original'} not deleted because it's assigned to rules: ${rulesWithAssignedCondition.join(', ')}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const deletedCondition = await this.conditionModel.findOneAndDelete(options).exec();

    if (!deletedCondition) {
      throw CreateException({
        message: `Condition ${name} for rulesPackage ${rulesPackage || 'original'} not deleted`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
    return deletedCondition;
  }

  @useFreezeChecker({ key: 0, isSearchQuery: true })
  async deleteFoundConditions(searchQuery: SearchQueryName): Promise<DeleteResult> {
    const options = getLikeQueryOptions(['name', 'rulesPackage'], searchQuery, true);
    const deleteResults = await this.conditionModel.deleteMany(options);

    if (!deleteResults.deletedCount) {
      throw CreateException({
        message: `Conditions with name like '${searchQuery.name}' not found and not deleted!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return deleteResults;
  }

  async getRulesWithAssignedCondition(
    conditionsName: string,
    rulesPackage: string | undefined
  ): Promise<RuleDocument[]> {
    const rulesData = await this.ruleModel.find({
      conditionsName,
      rulesPackage: rulesPackage || { $exists: false }
    });

    return rulesData;
  }
}
