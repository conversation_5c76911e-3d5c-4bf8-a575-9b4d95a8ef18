import { applyDecorators } from '@nestjs/common';
import { ApiResponse, ApiProduces, ApiBody } from '@nestjs/swagger';
import {
  PostConditionRequestModel,
  PostConditionResponseModel,
  ConditionErrorModel,
  GetConditionResponseModel,
  GetConditionByNameResponseModel,
  DeleteConditionByNameResponseModel
} from 'SwaggerModels/condition/conditionSwagger.models';

import { SimpleResponseModel } from 'SwaggerModels/swaggerCommonModels';

export function PostConditionDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiBody({
      type: PostConditionRequestModel
    }),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: PostConditionResponseModel
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request',
      type: ConditionErrorModel
    }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}

export function GetConditionDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: GetConditionResponseModel
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request',
      type: ConditionErrorModel
    }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}

export function DeleteConditionDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: SimpleResponseModel
    }),
    ApiResponse({ status: 400, description: 'Bad request' }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}

export function GetByNameConditionDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: GetConditionByNameResponseModel
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request',
      type: ConditionErrorModel
    }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}

export function PatchByNameConditionDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiBody({
      type: PostConditionRequestModel
    }),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: PostConditionResponseModel
    }),
    ApiResponse({
      status: 400,
      description: 'Bad request',
      type: ConditionErrorModel
    }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}

export function DeleteByNameConditionDoc(): ReturnType<typeof applyDecorators> {
  return applyDecorators(
    ApiProduces('application/json'),
    ApiResponse({
      status: 200,
      description: 'Valid response',
      type: DeleteConditionByNameResponseModel
    }),
    ApiResponse({ status: 400, description: 'Bad request' }),
    ApiResponse({ status: 500, description: 'Server error' })
  );
}
