import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { AllConditions, AnyConditions, LogLevel } from 'ads-layouts-tools';
import { FastifyRequest } from 'fastify';
import { getClientIp, searchQueryNameDto } from 'Helpers';
import { DeleteResult } from 'mongodb';
import { CreateException, log } from 'Utils';
import { ConditionService } from './conditions.service';
import {
  DeleteByNameConditionDoc,
  GetByNameConditionDoc,
  GetConditionDoc,
  PatchByNameConditionDoc,
  PostConditionDoc
} from './swagger/condition.doc.decorator';

@Controller('condition')
@ApiTags('Condition')
export class ConditionController {
  constructor(private readonly conditionService: ConditionService) {}

  getRequestData(req: FastifyRequest) {
    const reqIP = getClientIp(req);
    return { reqIP, reqHeaders: req.headers };
  }

  // CREATE
  @Post()
  @PostConditionDoc()
  async createCondition(
    @Req() req: FastifyRequest,
    @Body() newCondition: AllConditions | AnyConditions
  ) {
    const reqInfo = this.getRequestData(req);

    log('CONDITION_CREATE_REQUEST', { ...reqInfo, body: newCondition });
    try {
      const condition = await this.conditionService.createCondition(newCondition);

      const response = {
        status: 'condition created successfully',
        createdConditionDetails: condition
      };
      log('CONDITION_CREATE_RESPONSE', { ...reqInfo, response });

      return response;
    } catch (error) {
      log('ERROR_CONDITION_CREATE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // READ
  @Get()
  @GetConditionDoc()
  async getConditions(@Req() req: FastifyRequest, @Query() searchQuery: searchQueryNameDto) {
    const reqInfo = this.getRequestData(req);

    log('CONDITION_GET_ALL_REQUEST', { ...reqInfo });
    try {
      const conditionsData = await this.conditionService.getAllConditions(searchQuery);
      const response = {
        message: 'All conditions data found successfully',
        conditionsData
      };
      log('CONDITION_GET_ALL_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_CONDITION_GET_ALL', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Get('/:name/:rulesPackage?')
  @GetByNameConditionDoc()
  async getCondition(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined
  ) {
    const reqInfo = this.getRequestData(req);

    log('CONDITION_GET_BY_NAME_REQUEST', { ...reqInfo });
    try {
      const existingCondition = await this.conditionService.getCondition(name, rulesPackage);
      const response = {
        message: 'Condition data found successfully',
        existingCondition
      };
      log('CONDITION_GET_BY_NAME_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_CONDITION_GET_BY_NAME', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // UPDATE
  @Patch('/:name/:rulesPackage?')
  @PatchByNameConditionDoc()
  async updateCondition(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined,
    @Body() updatedCondition: Partial<AllConditions> | Partial<AnyConditions>
  ) {
    const reqInfo = this.getRequestData(req);

    log('CONDITION_UPDATE_REQUEST', { ...reqInfo, name, rulesPackage, updatedCondition });
    try {
      const existingCondition = await this.conditionService.updateCondition(
        name,
        rulesPackage,
        updatedCondition
      );
      const response = {
        message: `Condition ${name} for rulesPackage ${rulesPackage || 'original'} has been successfully updated`,
        existingCondition
      };
      log('CONDITION_UPDATE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_UPDATE_REQUEST', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  // DELETE
  @Delete('/:name/:rulesPackage?')
  @DeleteByNameConditionDoc()
  async deleteCondition(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined
  ) {
    const reqInfo = this.getRequestData(req);

    log('CONDITION_DELETE_REQUEST', { ...reqInfo, name });

    try {
      const deletedCondition = await this.conditionService.deleteCondition(name, rulesPackage);
      const response = {
        message: `Condition ${name} for rulesPackage ${rulesPackage || 'original'} has been deleted successfully`,
        deletedCondition
      };
      log('CONDITION_DELETE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_CONDITION_DELETE', { ...reqInfo, error }, LogLevel.error);

      throw CreateException(error);
    }
  }

  @Delete('/')
  async deleteFoundConditions(@Query() searchQuery: searchQueryNameDto) {
    try {
      const deleteConditions: DeleteResult =
        await this.conditionService.deleteFoundConditions(searchQuery);

      const response = {
        status: 'Conditions deleted successfully',
        deletedRulesPackagesDetails: deleteConditions
      };

      log('EVENTS_DELETED_RESPONSE', { response });
      return response;
    } catch (error) {
      log('ERROR_EVENTS_DELETE', { error: error.response }, LogLevel.error);
      throw error;
    }
  }
}
