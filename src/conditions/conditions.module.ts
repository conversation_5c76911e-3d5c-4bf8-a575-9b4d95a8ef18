import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  AllConditions,
  AllConditionsSchema,
  AnyConditions,
  AnyConditionsSchema,
  Conditions,
  ConditionsSchema,
  Rule,
  RuleSchema
} from 'ads-layouts-tools';
import { RulesPackagesModule } from '../rulesPackages/rulesPackages.module';
import { ConditionController } from './conditions.controller';
import { ConditionService } from './conditions.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Conditions.name,
        schema: ConditionsSchema,
        discriminators: [
          { name: AnyConditions.name, schema: AnyConditionsSchema },
          { name: AllConditions.name, schema: AllConditionsSchema }
        ]
      },
      {
        name: Rule.name,
        schema: RuleSchema
      }
    ]),
    RulesPackagesModule
  ],
  controllers: [ConditionController],
  providers: [ConditionService]
})
export class ConditionModule {}
