import { PlaceholdersConfig, Prettify } from 'ads-layouts-tools';

export type FetchType = {
  fetchHttpStatus: number;
  releaseUrl: URL;
  releaseName: string;
};

export type LogResponseType = {
  successfulFetch: FetchType[];
  failedFetch: FetchType[];
  configs: LoadResult | null;
};

export type ConfigData = {
  config_name: string;
  serviceId: string[];
  pageType: string[];
  pageId: string[];
  section: { id: string; name: string }[];
  config: PlaceholdersConfig;
};

export type FetchData = {
  data: ConfigData[];
  src: string;
};

export type AuditData = {
  releaseVersion: string;
  modifiedDate: string;
  generatedBy: string;
};

export type FetchedConfigs = {
  configs: FetchData[];
  auditData: AuditData;
};

export type AdditionalData = {
  releaseUrl: URL;
  releaseName: string;
};

export interface ConfigLogData {
  configName: string;
  configSrc: string;
  configServiceId: string;
  releaseUrl: URL;
  releaseName: string;
  releaseServices: string[];
}

export enum ResultType {
  inserted = 'inserted',
  updated = 'updated',
  skipped = 'skipped',
  failed = 'failed'
}

export type LoadResult = {
  [key in keyof typeof ResultType as `${key}Count`]: number;
};

export type HappyStatus = ResultType.inserted | ResultType.updated | ResultType.skipped;

export type HappyResult = Record<HappyStatus, ConfigLogData[]>;
export type FailedResult = { configLogData: ConfigLogData; error: any };

export type result =
  | { status: HappyStatus; configLogData: ConfigLogData }
  | { status: ResultType.failed; configLogData: ConfigLogData; error: any };

export type ResultingLoad = Prettify<HappyResult & { failed: FailedResult[] }>;
