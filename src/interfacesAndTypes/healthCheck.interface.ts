import { DisplayType, Prettify } from 'ads-layouts-tools';

export type HCError = Pick<Error, 'message' | 'stack'>;

export interface IReleaseVersionComparisonBody {
  config: string;
  urlList: string[];
}

export interface IComparableRulesType {
  ruleName: string;
  rulesPackage: string;
  conditionName: string;
  eventName: string;
}

export interface IRawDebug {
  raw: boolean;
  debug: boolean;
}

export interface IGeneratorResponsePartial {
  requestMeta: { serviceEnv?: string; serviceId: string; siteVersion?: string; time: string };
  version: string;
}

export type GeneratorAdMapsSuccess = {
  fetchedAdMap: true;
  admapUrl: URL;
  admapBody: object; // CommonRequest
};

export type GeneratorAdMapsFailed = {
  fetchedAdMap: false;
  admapUrl: URL;
  error: HCError;
};

export type GeneratorAdMapsResult = GeneratorAdMapsSuccess | GeneratorAdMapsFailed;

export type ExtractResultPassed = {
  fetchedAdMap: true;
  generatorResponded: true;
  admapUrl: URL;
  serviceId: string;
  siteVersion?: string;
  serviceEnv?: string;
  time: string;
  version: string;
};

export type ExtractResultFailed = {
  fetchedAdMap: true;
  generatorResponded: false;
  admapUrl: URL;
  error: HCError;
};

export type ExtractResult = Prettify<
  ExtractResultPassed | ExtractResultFailed | GeneratorAdMapsFailed
>;

export type ComparisonResult = {
  fetchedAdMap: true;
  generatorResponded: true;
  versionsEqual: boolean;
  versionWorker: string;
  versionBackend: string;
  admapUrl: URL;
};

export type ReleaseVersionComparisonResult = Prettify<
  ComparisonResult | ExtractResultFailed | GeneratorAdMapsFailed
>;

export interface VersionComparisonResponse {
  comparison: boolean;
  lambdaSDKVersions: DisplayType[];
  AdsLayoutsDBVersions: DisplayType[];
}
