import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Cron, CronExpression } from '@nestjs/schedule';
import { log } from 'Utils';
import {
  CachePartsEnum,
  LogLevel,
  WorkersCacheConfig,
  WorkersCacheConfigDocument
} from 'ads-layouts-tools';
import dayjs from 'dayjs';
import { DeleteResult } from 'mongodb';
import { Model } from 'mongoose';

@Injectable()
export class WorkersCacheConfigService {
  constructor(
    @InjectModel(WorkersCacheConfig.name)
    private workersCacheConfigModel: Model<WorkersCacheConfigDocument>
  ) {}

  getWorkerCacheConfig = async () => {
    const workersCacheConfigs = await this.workersCacheConfigModel
      .find({})
      .select('-createdAt -updatedAt -_id -__v');

    return workersCacheConfigs;
  };

  deleteAll = async (): Promise<DeleteResult> => {
    const workersCacheConfigs = await this.workersCacheConfigModel.deleteMany({});

    return workersCacheConfigs;
  };

  @Cron(CronExpression.EVERY_DAY_AT_3AM, {
    name: 'BEdeleteAllWorkerCacheConfigsAt3AM',
    timeZone: 'Europe/Warsaw'
  })
  async deleteAllScheduled(): Promise<void> {
    const result = await this.workersCacheConfigModel.deleteMany({});

    log('DELETE_ALL_WORKER_CACHE_CONFIGS', { deleteResult: result }, LogLevel.cache);
  }

  addWorkerCacheConfig = async (serviceId: string, cachePart: CachePartsEnum) => {
    log('ADD_WORKER_CACHE_INVOKED', { serviceId, cachePart });
    try {
      const timestamp = Date.now();
      const insert = new this.workersCacheConfigModel({
        timestamp: Date.now(),
        formatedDate: dayjs(timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        serviceId,
        cachePart
      });

      log('ADD_WORKER_CACHE_INSERT_NEW', { insert });

      await insert.save();

      return 'OK';
    } catch (err) {
      log('ERROR_ADD_WORKER_CACHE', { err }, LogLevel.error);
      throw new BadRequestException(err?.message);
    }
  };
}
