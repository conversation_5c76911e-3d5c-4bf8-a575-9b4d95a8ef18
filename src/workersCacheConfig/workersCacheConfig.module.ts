import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { WorkersCacheConfigService } from './workersCacheConfig.service';
import { WorkersCacheConfig, WorkersCacheConfigSchema } from 'ads-layouts-tools';
import { WorkersCacheConfigController } from './workersCacheConfig.controller';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: WorkersCacheConfig.name,
        schema: WorkersCacheConfigSchema
      }
    ])
  ],
  controllers: [WorkersCacheConfigController],
  providers: [WorkersCacheConfigService]
})
export class WorkersCacheConfigModule {}
