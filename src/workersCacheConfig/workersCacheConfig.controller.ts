import { Controller, Delete, Get, Post, Query } from '@nestjs/common';
import { CachePartsEnum } from 'ads-layouts-tools';
import { log } from 'Utils';
import { WorkersCacheConfigService } from './workersCacheConfig.service';

@Controller('workersCacheConfig')
export class WorkersCacheConfigController {
  constructor(private readonly workersCacheConfigService: WorkersCacheConfigService) {}

  @Post()
  async addWorkerCacheConfig(
    @Query() query: { serviceId: string; cachePart: CachePartsEnum }
  ) {
    try {
      const { serviceId, cachePart } = query;

      const response = await this.workersCacheConfigService.addWorkerCacheConfig(
        serviceId,
        cachePart
      );

      if (response === 'OK') {
        log('ADDED_NEW_WORKER_CACHE_CONFIG', { serviceId, cachePart });

        return {
          status: 'added new config successfully'
        };
      }
    } catch (error) {
      return error;
    }
  }

  @Get()
  async getWorkerCacheConfig() {
    try {
      const allConfigs = await this.workersCacheConfigService.getWorkerCacheConfig();
      log('GET_ALL_WORKER_CACHE_CONFIG', { allConfigs });
      return allConfigs;
    } catch (error) {
      return error;
    }
  }

  @Delete()
  async deleteAll() {
    try {
      const response = await this.workersCacheConfigService.deleteAll();

      log('DELETE_ALL_WORKER_CACHE_CONFIGS', { response });
      return response;
    } catch (error) {
      return error;
    }
  }
}
