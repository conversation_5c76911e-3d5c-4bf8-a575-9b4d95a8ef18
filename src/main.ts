import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import { initSwagger } from './initSwagger';
import { JoiPipe } from 'nestjs-joi';
import {
  MongoExceptionFilter,
  HttpExceptionFilter,
  MongooseValidatorExceptionFilter
} from 'Errors/exceptionFilters';
import { validators } from './envalidConfig';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter()
  );

  app.useGlobalPipes(new JoiPipe());
  app.useGlobalFilters(
    new MongoExceptionFilter(),
    new HttpExceptionFilter(),
    new MongooseValidatorExceptionFilter()
  );

  initSwagger(app);

  await app.listen(validators.APP_PORT, validators.APP_ADDRESS);
}
bootstrap();
