import { NestFastifyApplication } from '@nestjs/platform-fastify';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { InfoModule } from './info/info.module';
import { AdConfigModule } from './adConfigs/adConfig.module';
import { RulesModule } from './rules/rules.module';
import { EventModule } from './events/event.module';
import { ConditionModule } from './conditions/conditions.module';

export function initSwagger(app: NestFastifyApplication): void {
  const config = new DocumentBuilder().setTitle('Ads Layouts').build();

  const swaggerCustomOptions = {
    // customCss: ".swagger-ui section.models { visibility: hidden;}",
    swaggerOptions: {
      tagsSorter: 'alpha',
      operationsSorter: 'alpha'
    }
  };

  const document = SwaggerModule.createDocument(app, config, {
    include: [RulesModule, ConditionModule, EventModule, AdConfigModule, InfoModule]
  });

  SwaggerModule.setup('doc', app, document, swaggerCustomOptions);
}
