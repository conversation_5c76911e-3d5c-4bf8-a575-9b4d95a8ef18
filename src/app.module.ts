import { Module } from '@nestjs/common';
import { InfoModule } from './info/info.module';
import { MongooseModule } from '@nestjs/mongoose';
import { RulesModule } from './rules/rules.module';
import { AdConfigModule } from './adConfigs/adConfig.module';
import { EventModule } from './events/event.module';
import { ConditionModule } from './conditions/conditions.module';
import { ScheduleModule } from '@nestjs/schedule';
import { DisplayConfigModule } from './displayConfig/displayConfig.module';
import { ExtensionModule } from './extensionConfig/extension.module';
import { VariantWeightsConfigModule } from './variantWeightsConfig/variantWeightsConfig.module';
import { WorkersCacheConfigModule } from './workersCacheConfig/workersCacheConfig.module';
import { RulesPackagesModule } from './rulesPackages/rulesPackages.module';
import { ServiceToPackageMapModule } from './serviceToPackageMap/serviceToPackageMap.module';
import { validators } from './envalidConfig';
import { HealthCheckModule } from './healthCheck/healthCheck.module';

@Module({
  imports: [
    MongooseModule.forRoot(validators.MONGO_HOST, {
      dbName: validators.MONGO_DB_NAME,
      pass: validators.MONGO_PASS,
      user: validators.MONGO_USER,
      autoCreate: false,
      autoIndex: true
    }),
    InfoModule,
    RulesModule,
    EventModule,
    AdConfigModule,
    DisplayConfigModule,
    ExtensionModule,
    ConditionModule,
    VariantWeightsConfigModule,
    WorkersCacheConfigModule,
    RulesPackagesModule,
    ScheduleModule.forRoot(),
    ServiceToPackageMapModule,
    HealthCheckModule
  ]
})
export class AppModule {}
