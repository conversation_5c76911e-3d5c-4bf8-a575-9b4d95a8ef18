import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Event, LogLevel } from 'ads-layouts-tools';
import { FastifyRequest } from 'fastify';
import { getClientIp, searchQueryNameDto } from 'Helpers';
import { IDeleteFoundEventsResponse } from 'InterfacesAndTypes';
import { CreateException, log } from 'Utils';
import { EventService } from './event.service';

@Controller('event')
@ApiTags('Event')
export class EventController {
  constructor(private readonly eventService: EventService) {}

  getRequestData(req: FastifyRequest) {
    const reqIP = getClientIp(req);
    return { reqIP, reqHeaders: req.headers };
  }

  // CREATE
  @Post()
  async createEvent(@Req() req: FastifyRequest, @Body() newEvent: Event) {
    const reqInfo = this.getRequestData(req);

    log('EVENT_CREATE_REQUEST', { ...reqInfo });

    try {
      const event = await this.eventService.createEvent(newEvent);
      const response = {
        status: 'event created successfully',
        createdEventDetails: event
      };

      log('EVENT_CREATE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_EVENT_CREATE', { ...reqInfo, error }, LogLevel.error);
      throw CreateException(error);
    }
  }

  // READ
  @Get()
  async getEvents(@Req() req: FastifyRequest, @Query() searchQuery: searchQueryNameDto) {
    const reqInfo = this.getRequestData(req);

    log('EVENT_GET_ALL_REQUEST', { ...reqInfo });
    try {
      const eventsData = await this.eventService.getAllEvents(searchQuery);
      const response = {
        message: 'All events data found successfully',
        eventsData
      };
      log('EVENT_GET_ALL_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_EVENT_GET_ALL', { ...reqInfo, error }, LogLevel.error);
      throw CreateException(error);
    }
  }

  @Get('/:name/:rulesPackage?')
  async getEvent(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined
  ) {
    const reqInfo = this.getRequestData(req);

    log('EVENT_GET_BY_NAME_REQUEST', { ...reqInfo, name });
    try {
      const existingEvent = await this.eventService.getEvent(name, rulesPackage);
      const response = {
        message: 'Event data found successfully',
        existingEvent
      };
      log('EVENT_GET_BY_NAME_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_EVENT_GET_BY_NAME', { ...reqInfo, error }, LogLevel.error);
      throw CreateException(error);
    }
  }

  // UPDATE
  @Patch('/:name/:rulesPackage?')
  async updateEvent(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined,
    @Body() updatedEvent: Partial<Event>
  ) {
    const reqInfo = this.getRequestData(req);

    log('EVENT_UPDATE_REQUEST', { ...reqInfo, name, rulesPackage, body: updatedEvent });
    try {
      const existingEvent = await this.eventService.updateEvent(
        name,
        rulesPackage,
        updatedEvent
      );
      const response = {
        message: `Event ${name} for rulesPackage ${rulesPackage || 'original'} has been successfully updated`,
        existingEvent
      };
      log('EVENT_UPDATE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_EVENT_UPDATE', { ...reqInfo, error }, LogLevel.error);
      throw CreateException(error);
    }
  }

  // DELETE
  @Delete('/:name/:rulesPackage?')
  async deleteEvent(
    @Req() req: FastifyRequest,
    @Param('name') name: string,
    @Param('rulesPackage') rulesPackage: string | undefined
  ) {
    const reqInfo = this.getRequestData(req);

    log('EVENT_DELETE_REQUEST', { ...reqInfo, name, rulesPackage });
    try {
      const deletedEvent = await this.eventService.deleteEvent(name, rulesPackage);
      const response = {
        message: `Event ${name} for rulesPackage ${rulesPackage || 'original'} has been deleted successfully`,
        deletedEvent
      };
      log('EVENT_DELETE_RESPONSE', { ...reqInfo, response });
      return response;
    } catch (error) {
      log('ERROR_EVENT_DELETE', { ...reqInfo, error }, LogLevel.error);
      throw CreateException(error);
    }
  }

  @Delete('/')
  async deleteFoundEvents(
    @Query() searchQuery: searchQueryNameDto
  ): Promise<IDeleteFoundEventsResponse> {
    try {
      const deleteEvents = await this.eventService.deleteFoundEvents(searchQuery);

      const response = {
        status: 'Events deleted successfully',
        deletedRulesPackagesDetails: deleteEvents
      };

      log('EVENTS_DELETED_RESPONSE', { response });
      return response;
    } catch (error) {
      log('ERROR_EVENTS_DELETE', { error: error.response }, LogLevel.error);
      throw CreateException(error);
    }
  }
}
