import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Event, Rule, RuleSchema } from 'ads-layouts-tools';
import { RulesPackagesModule } from '../rulesPackages/rulesPackages.module';
import { EventSchema } from '../schema/dbIndices';
import { EventController } from './event.controller';
import { EventService } from './event.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: Event.name,
        schema: EventSchema
      }
    ]),
    MongooseModule.forFeature([
      {
        name: Rule.name,
        schema: RuleSchema
      }
    ]),
    RulesPackagesModule
  ],
  controllers: [EventController],
  providers: [EventService]
})
export class EventModule {}
