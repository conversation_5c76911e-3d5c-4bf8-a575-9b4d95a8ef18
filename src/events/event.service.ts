import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Event, EventDocument, Rule, RuleDocument, SearchQueryName } from 'ads-layouts-tools';
import { getFetchRuleOptions, getLikeQueryOptions, useFreezeChecker } from 'Helpers';
import { DeleteResult } from 'mongodb';
import { Model, ModifyResult } from 'mongoose';
import { CreateException } from 'Utils';
import { RulesPackagesService } from '../rulesPackages/rulesPackages.service';

@Injectable()
export class EventService {
  constructor(
    private readonly rulesPackagesService: RulesPackagesService,
    @InjectModel(Event.name) private eventModel: Model<EventDocument>,
    @InjectModel(Rule.name) private ruleModel: Model<RuleDocument>
  ) {}

  // CREATE
  async createEvent(event: Event): Promise<Event> {
    const newEvent = new this.eventModel(event);
    try {
      await newEvent.save();
      return newEvent;
    } catch (err) {
      throw new BadRequestException(err?.message);
    }
  }

  // READ
  async getAllEvents(searchQuery: SearchQueryName): Promise<EventDocument[] | string[]> {
    const returnOnlyNames = searchQuery.onlyNames;
    const options = getLikeQueryOptions(['name', 'rulesPackage'], searchQuery);
    const eventData = await this.eventModel.find(options);

    if (!eventData || eventData.length == 0) {
      throw new NotFoundException('Events data not found!');
    }

    if (returnOnlyNames) {
      return eventData.map(event => event.name);
    }

    return eventData;
  }

  async getEvent(name: string, rulesPackage: string | undefined): Promise<EventDocument> {
    const existingEvent = await this.eventModel.findOne({ name, rulesPackage }).exec();
    if (!existingEvent) {
      throw new NotFoundException(
        `Event ${name} for rulesPackage ${rulesPackage || 'original'} not found`
      );
    }
    return existingEvent;
  }

  // UPDATE
  @useFreezeChecker({ key: 1, isSearchQuery: false })
  async updateEvent(
    eventName: string,
    rulesPackage: string | undefined,
    updatedEvent: Partial<Event>
  ): Promise<EventDocument> {
    const options = getFetchRuleOptions(eventName, rulesPackage);

    const existingEvent = await this.eventModel
      .findOneAndUpdate(options, updatedEvent, { new: true, runValidators: true })
      .exec();

    if (!existingEvent) {
      throw new BadRequestException(
        `Event ${eventName} for rulesPackage ${rulesPackage || 'original'} not updated`
      );
    }

    return existingEvent;
  }

  // DELETE
  @useFreezeChecker({ key: 1, isSearchQuery: false })
  async deleteEvent(
    name: string,
    rulesPackage: string | undefined
  ): Promise<ModifyResult<EventDocument>> {
    const options = getFetchRuleOptions(name, rulesPackage);

    const rulesWithAssignedEvent =
      (await this.getRulesWithAssignedEvent(name, rulesPackage))?.map(
        rule => `${rule.name} (${rule.rulesPackage || 'original'})`
      ) || [];

    if (rulesWithAssignedEvent.length) {
      throw CreateException({
        message: `Event ${name} for rulesPackage ${rulesPackage || 'original'} not deleted because it's assigned to rules: ${rulesWithAssignedEvent.join(', ')}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }

    const deletedEvent = await this.eventModel.findOneAndDelete(options);

    if (!deletedEvent) {
      throw new BadRequestException(
        `Event ${name} for rulesPackage ${rulesPackage || 'original'} not deleted`
      );
    }
    return deletedEvent;
  }

  @useFreezeChecker({ key: 0, isSearchQuery: true })
  async deleteFoundEvents(searchQuery: SearchQueryName): Promise<DeleteResult> {
    const options = getLikeQueryOptions(['name', 'rulesPackage'], searchQuery, true);
    const deleteResults = await this.eventModel.deleteMany(options);

    if (!deleteResults.deletedCount) {
      throw CreateException({
        message: `Events with name like '${searchQuery.name}' not found and not deleted!`,
        statusCode: HttpStatus.NOT_FOUND
      });
    }

    return deleteResults;
  }

  async getRulesWithAssignedEvent(
    eventName: string,
    rulesPackage: string | undefined
  ): Promise<RuleDocument[]> {
    const rulesData = await this.ruleModel.find({
      eventName,
      rulesPackage: rulesPackage || { $exists: false }
    });

    return rulesData;
  }
}
