import { BadRequestException, Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

@Controller('check')
@ApiTags('Info')
export class InfoController {
  @Get('/')
  appCheck(): string {
    try {
      return 'OK';
    } catch (error) {
      throw new BadRequestException(error?.message);
    }
  }

  @Get('/version')
  appVersion(): string {
    try {
      return process.env.npm_package_version ?? 'NO VERSION FOUND';
    } catch (error) {
      throw new BadRequestException(error?.message);
    }
  }
}
