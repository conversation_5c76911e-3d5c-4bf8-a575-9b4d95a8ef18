import { between0And100, days, minutes, ServiceEnvEnum, ServiceName } from 'ads-layouts-tools';
import * as dotenv from 'dotenv';
import { cleanEnv, host, port, str, url } from 'envalid';
dotenv.config();

export const validators = cleanEnv(process.env, {
  // MONGO
  MONGO_HOST: url({
    default:
      'mongodb://127.0.0.1:27017/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.3.7'
  }),
  MONGO_PORT: port({ default: 27017 }),
  MONGO_DB_NAME: str({ default: 'adsLayouts' }),
  MONGO_PASS: str({ default: '' }),
  MONGO_USER: str({ default: '' }),

  //APP
  APP_PORT: port({ default: 4000 }),
  APP_ADDRESS: host({ default: '0.0.0.0' }),
  SERVICE_NAME: str<ServiceName>({ default: ServiceName.BACKEND }),

  // DATADOG
  SEND_DEV_LOGS_TO_DATADOG: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),
  SEND_CACHE_LOGS_TO_DATADOG: str({
    choices: ['ENABLED', 'DISABLED'],
    default: 'ENABLED'
  }),

  APP_ENV: str({
    default: ServiceEnvEnum.LOCAL,
    choices: Object.values(ServiceEnvEnum)
  }),
  LOGS_COLORING: str({ choices: ['ENABLED', 'DISABLED'], default: 'ENABLED' }),

  DISPLAY_CONFIG_EXT_URL: url({
    default: 'https://display-at.cdntvn.pl/lambdaDisplayConfigs/configExt.json'
  }),
  EXTENSION_REFRESH_INTERVAL_IN_MINUTES: minutes({ default: 10 }),

  //STAGE
  DISPLAY_CONFIG_URL: url({
    default: 'https://stage-display-at.cdntvn.pl/lambdaDisplayConfigs/config.json'
  }),
  FULL_JSON_URL: url({
    default: 'https://stage-display-at.cdntvn.pl/sdk-display/configs/'
  }),

  //ADCONFIG
  MAX_DELETION_PERCENTAGE: between0And100({ default: 50 }),
  DAYS_EXPIRATION_INTERVAL: days({ default: 7 }),

  HC_RELEASE_VERSIONS_LOCATION_URL: url({
    default: `https://stage-at-adslayouts.tvn.pl/generator?debug=true`
  }),
  HC_RELEASE_VERSIONS_CDN_URL: str({
    default: `https://$PREFIX_CDNdisplay-at.cdntvn.pl/lambdaDisplayConfigs/config.json`
  }),
  HC_RELEASE_VERSIONS_ALLOWED_HOSTS: str({
    default:
      'https://tvn24.pl;https://dziendobry.tvn.pl;https://cozatydzien.tvn.pl;https://tvn.pl'
  })
});
