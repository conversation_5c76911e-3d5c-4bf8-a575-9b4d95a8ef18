import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ExtensionService } from './extension.service';
import { ExtensionController } from './extension.controller';
import { ExtensionConfig, ExtensionConfigSchema } from 'ads-layouts-tools';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ExtensionConfig.name,
        schema: ExtensionConfigSchema
      }
    ])
  ],
  controllers: [ExtensionController],
  providers: [ExtensionService]
})
export class ExtensionModule {}
