import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ExtensionConfig, ExtensionConfigDocument, LogLevel } from 'ads-layouts-tools';
import dayjs from 'dayjs';
import { Model } from 'mongoose';
import fetch from 'node-fetch';
import { log } from 'Utils';
import { validators } from '../envalidConfig';

@Injectable()
export class ExtensionService {
  constructor(
    @InjectModel(ExtensionConfig.name)
    private extensionConfigModel: Model<ExtensionConfigDocument>
  ) {}

  async deleteExtensionConfig() {
    const deleteResult = await this.extensionConfigModel.deleteMany({});

    log('DELETED_ALL_EXTENSION_CONFIGS', { deleteResult });
  }

  async getExtensionConfigStatus() {
    const extensionConfing = await this.extensionConfigModel.findOne();

    return extensionConfing;
  }

  handleExtensionConfig = async () => {
    log('EXTENSION_RESET_INVOKED');
    try {
      const extensionRefreshInterval = validators.EXTENSION_REFRESH_INTERVAL_IN_MINUTES;

      const currentConfig = await this.extensionConfigModel.findOne({});
      const timestamp = currentConfig?.timestamp ?? 0;
      const dateIsNotFuture = timestamp + extensionRefreshInterval < Date.now();

      if (dateIsNotFuture || !currentConfig?.timestamp) {
        const displayConfigResponseExt = await fetch(validators.DISPLAY_CONFIG_EXT_URL);
        const extensionConfig = await displayConfigResponseExt.json();
        const {
          audit,
          superPanelEnabled,
          superPanelSchedule,
          topPremiumEnabled,
          topPremiumSchedule
        } = extensionConfig;
        const extConfigDate = dayjs(audit?.modifiedDate);
        const currentConfigDate = currentConfig && dayjs(currentConfig?.audit.modifiedDate);

        if (!currentConfigDate || currentConfigDate.isBefore(extConfigDate)) {
          await this.deleteExtensionConfig();

          const insert = new this.extensionConfigModel({
            timestamp: Date.now(),
            audit,
            superPanelEnabled,
            superPanelSchedule, // na czas testów, docelowo nie jest częścią rozwiązania bo obsługa super panelu jest realizowana przez lambdę
            topPremiumEnabled,
            topPremiumSchedule
          });

          log('EXTENSION_INSERT_NEW', { insert });
          await insert.save();

          return 'OK';
        }
      }

      log('EXTENSION_RESET_NOT_REQUIRED');
    } catch (err) {
      log('ERROR_LOAD_SUPER_PANEL_CONFIG', { err }, LogLevel.error);
      throw new BadRequestException(err?.message);
    }
  };
}
