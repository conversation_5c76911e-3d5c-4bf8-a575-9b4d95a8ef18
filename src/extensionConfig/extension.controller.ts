import { Controller, Get } from '@nestjs/common';
import { ExtensionService } from './extension.service';
import { Interval } from '@nestjs/schedule';
import { validators } from '../envalidConfig';

@Controller('extension')
export class ExtensionController {
  constructor(private readonly extensionService: ExtensionService) {}

  @Interval(validators.EXTENSION_REFRESH_INTERVAL_IN_MINUTES)
  @Get('/reset')
  async resetExtensionConfig() {
    try {
      const response = await this.extensionService.handleExtensionConfig();

      if (response === 'OK') {
        return {
          status: 'extension config reseted successfully'
        };
      }

      return {
        status: 'extension config reset not required'
      };
    } catch (error) {
      return error;
    }
  }

  @Get()
  async getExtensionConfigStatus() {
    try {
      const response = await this.extensionService.getExtensionConfigStatus();
      return response;
    } catch (error) {
      return error;
    }
  }
}
