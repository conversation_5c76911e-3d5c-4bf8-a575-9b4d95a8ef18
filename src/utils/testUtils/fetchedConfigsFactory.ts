import { FetchedConfigs } from 'InterfacesAndTypes';
import { AdConfig } from 'ads-layouts-tools';
import { DEFAULT_RELEASE_NAME } from './adConfigFactory';

export const fetchedConfigsDefaultFactory = (data: AdConfig[]): FetchedConfigs => ({
  configs: [{ src: 'Default_src', data }],
  auditData: {
    releaseVersion: DEFAULT_RELEASE_NAME,
    modifiedDate: new Date().toISOString(),
    generatedBy: 'Default_user'
  }
});
