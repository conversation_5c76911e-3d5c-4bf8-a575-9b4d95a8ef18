import {
  AdConfig,
  AdConfigDeviceTypeEnum,
  PlaceholdersDetails,
  Prettify
} from 'ads-layouts-tools';
import { placeholderFactory } from 'Utils';

export type PartialPlaceholder = Pick<PlaceholdersDetails, 'deviceType' | 'id'> &
  Partial<Pick<PlaceholdersDetails, 'enabled'>>;

export type AdConfigFactoryParams = Prettify<
  Pick<AdConfig, 'config_name' | 'pageType' | 'serviceId' | 'pageId' | 'section'> &
    Partial<Pick<AdConfig['config'], 'masterId'>> &
    Pick<AdConfig['adsLayoutsAdditionalData'], 'releaseServices' | 'releaseName'> & {
      placeholders: PartialPlaceholder[];
    }
>;

const { DESKTOP, SMARTPHONE, TABLET } = AdConfigDeviceTypeEnum;

export const adConfigFactory = ({
  releaseServices,
  releaseName,
  config_name,
  pageType,
  serviceId,
  placeholders,
  pageId = [],
  section = [],
  masterId = ''
}: AdConfigFactoryParams): AdConfig => ({
  adsLayoutsAdditionalData: {
    releaseServices,
    releaseUrl: new URL('https://example.com/'), // Placeholder URL, should be replaced with actual logic
    releaseName
  },
  auditData: {
    releaseVersion: releaseName.replace(/\/$/, ''), // cut the last slash
    modifiedDate: '',
    generatedBy: ''
  },
  config_name,
  src: '',
  pageType,
  serviceId,
  pageId,
  section,
  config: {
    masterId,
    bgPlugSrc: '',
    activationThresholds: { offset: null, percent: null, delay: 0 },
    trafficCategory: [],
    placeholders: placeholders.map(placeholderFactory)
  }
});

export const DEFAULT_PAGE_ID = 'Default_page_id';
export const DEFAULT_RELEASE_NAME = 'Default_release/0.0.0/';

export const AdConfigDefaultParameters = (
  masterId: string,
  releaseName = DEFAULT_RELEASE_NAME
): AdConfigFactoryParams => ({
  masterId,

  releaseServices: ['Default_release_services'],
  releaseName,
  config_name: 'Default_config_name',
  pageType: ['Default_page_type'],
  serviceId: ['Default_service_id'],
  pageId: [DEFAULT_PAGE_ID],
  section: [{ id: 'Default_section_id', name: 'Default_section_name' }],
  placeholders: [
    { id: `${masterId}_Default_placeholder`, deviceType: [DESKTOP] },
    { id: `${masterId}_Default_placeholder`, deviceType: [SMARTPHONE] },
    { id: `${masterId}_Default_placeholder`, deviceType: [TABLET] }
  ]
});

export const adConfigDefaultFactory = (masterId: string, releaseName?: string): AdConfig =>
  adConfigFactory(AdConfigDefaultParameters(masterId, releaseName));
