import { PlaceholdersDetails, AdConfigAdServerEnum } from 'ads-layouts-tools';

export const placeholderFactory = ({
  id,
  deviceType,
  enabled = true
}: Pick<PlaceholdersDetails, 'id' | 'deviceType'> &
  Partial<Pick<PlaceholdersDetails, 'enabled'>>): PlaceholdersDetails => ({
  id,
  deviceType,
  enabled,

  // defaults
  adSlots: [
    { adServer: AdConfigAdServerEnum.ADOCEAN },
    { adServer: AdConfigAdServerEnum.GAM }
  ],
  width: '',
  height: '',
  adServer: AdConfigAdServerEnum.GAM,
  code: '',
  AD_Config_group: '',
  AD_Config_element_id: '',
  bidders: [],
  mediaTypes: { banner: [] },
  activationThresholds: { offset: null, percent: null, delay: 0 }
});
