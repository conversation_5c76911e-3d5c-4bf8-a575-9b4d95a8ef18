import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ServiceToPackageMapService } from './serviceToPackageMap.service';
import { ServiceToPackageMapController } from './serviceToPackageMap.controller';
import { ServiceToPackageMap } from 'ads-layouts-tools';
import { ServiceToPackageMapSchema } from '../schema/dbIndices';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: ServiceToPackageMap.name,
        schema: ServiceToPackageMapSchema
      }
    ])
  ],
  controllers: [ServiceToPackageMapController],
  providers: [ServiceToPackageMapService],
  exports: [ServiceToPackageMapService]
})
export class ServiceToPackageMapModule {}
