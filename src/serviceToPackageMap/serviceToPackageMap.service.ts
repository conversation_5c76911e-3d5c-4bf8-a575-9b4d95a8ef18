import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  ResponseInterface,
  ServiceToPackageMap,
  ServiceToPackageMapDocument
} from 'ads-layouts-tools';
import { createResponse } from 'Helpers';
import { Model } from 'mongoose';
import { CreateException } from 'Utils';
import { CreateServiceToPackageMapDto } from './dto/createServiceToPackageMap.dto';
import { UpdateServiceToPackageMapDto } from './dto/updateServiceToPackageMap.dto';

@Injectable()
export class ServiceToPackageMapService {
  constructor(
    @InjectModel(ServiceToPackageMap.name)
    private serviceToPackageMapModel: Model<ServiceToPackageMapDocument>
  ) {}

  async create(singleServiceToPackageMap: CreateServiceToPackageMapDto) {
    try {
      const newServiceToPackageMap = new this.serviceToPackageMapModel(
        singleServiceToPackageMap
      );
      await newServiceToPackageMap.save();

      return createResponse(
        HttpStatus.CREATED,
        'New service to package map created successfully!',
        newServiceToPackageMap
      );
    } catch (err) {
      throw new BadRequestException(err?.message);
    }
  }

  async delete(mapToDelete: string) {
    const deletedMap = await this.serviceToPackageMapModel
      .findOneAndDelete({ rulesPackage: mapToDelete })
      .exec();

    if (!deletedMap) {
      throw new NotFoundException(
        `Service to package map with name: ${mapToDelete} not found and not deleted!`
      );
    }

    return createResponse(
      HttpStatus.OK,
      `Service to package map with name: ${mapToDelete} deleted successfully!`,
      deletedMap
    );
  }

  async findAll() {
    const allServiceToPackageMaps = await this.serviceToPackageMapModel.find({}).exec();

    if (!allServiceToPackageMaps || allServiceToPackageMaps.length == 0) {
      throw new NotFoundException('Any ServiceToPackageMap not found!');
    }

    return createResponse(
      HttpStatus.OK,
      `Collection of all ServiceToPackageMaps get with success!`,
      allServiceToPackageMaps
    );
  }

  async findOne(mapToFind: string) {
    const serviceToPackageMap = await this.serviceToPackageMapModel
      .findOne({ rulesPackage: mapToFind })
      .exec();

    if (!serviceToPackageMap) {
      throw new NotFoundException(`Map with rulesPackage name: ${mapToFind} not found!`);
    }

    return createResponse(
      HttpStatus.OK,
      `ServiceToPackageMap get with success!`,
      serviceToPackageMap
    );
  }

  async getMappedServices(rulesPackage: string) {
    const serviceToPackageMap = await this.serviceToPackageMapModel
      .findOne({ rulesPackage })
      .exec();

    return serviceToPackageMap?.serviceId;
  }

  async update(
    mapToUpdate: string,
    updateServiceToPackageMapDto: UpdateServiceToPackageMapDto
  ) {
    try {
      const updatedServiceToPackageMap = await this.serviceToPackageMapModel
        .findOneAndUpdate({ rulesPackage: mapToUpdate }, updateServiceToPackageMapDto, {
          new: true
        })
        .exec();

      if (!updatedServiceToPackageMap) {
        throw new NotFoundException(`Map with rulesPackage name: ${mapToUpdate} not found!`);
      }

      return createResponse(
        HttpStatus.OK,
        `ServiceToPackageMap updated with success!`,
        updatedServiceToPackageMap
      );
    } catch (error) {
      throw CreateException(error);
    }
  }

  async unbindServicesFromPackage(
    rulesPackageName: string,
    servicesListToUnbind: string[] | undefined
  ) {
    try {
      const serviceToPackageMapAfterUnbind = await this.serviceToPackageMapModel
        .findOneAndUpdate(
          { rulesPackage: rulesPackageName },
          { $pullAll: { serviceId: servicesListToUnbind } },
          { new: true }
        )
        .exec();

      if (!serviceToPackageMapAfterUnbind) {
        throw new NotFoundException(
          `Map with rulesPackage name: ${rulesPackageName} not found!`
        );
      }

      return createResponse(
        HttpStatus.OK,
        `Services [${servicesListToUnbind}] unbinded from rulesPackage ${rulesPackageName} with success!`,
        serviceToPackageMapAfterUnbind
      );
    } catch (error) {
      throw CreateException(error);
    }
  }

  async bindServicesFromPackage(
    rulesPackageName: string,
    servicesListToBind: string[] | undefined
  ) {
    try {
      const serviceToPackageMapAfterBind = await this.serviceToPackageMapModel
        .findOneAndUpdate(
          { rulesPackage: rulesPackageName },
          { $addToSet: { serviceId: { $each: servicesListToBind } } },
          { new: true }
        )
        .exec();

      if (!serviceToPackageMapAfterBind) {
        throw new NotFoundException(
          `Map with rulesPackage name: ${rulesPackageName} not found!`
        );
      }

      return createResponse(
        HttpStatus.OK,
        `Services [${servicesListToBind}] binded to rulesPackage ${rulesPackageName} with success!`,
        serviceToPackageMapAfterBind
      );
    } catch (error) {
      throw CreateException(error);
    }
  }

  async getServiceToPackageMapIndexes() {
    try {
      const serviceToPackageMapIndexes =
        await this.serviceToPackageMapModel.collection.indexes();

      return [{ serviceToPackageMapIndexes }];
    } catch (err) {
      return createResponse(
        HttpStatus.BAD_REQUEST,
        `Get serviceToPackageMap Indexes were wrong! ${err?.message}`
      );
    }
  }

  async dropAllIndexes(): Promise<ResponseInterface> {
    try {
      await this.serviceToPackageMapModel.collection.dropIndexes();

      return createResponse(
        HttpStatus.OK,
        'All indexes from serviceToPackageMap collection removed successfully!'
      );
    } catch (err) {
      throw new BadRequestException(err?.message);
    }
  }

  async createServiceToPackageMapIndexes() {
    try {
      const conditionIndexes = await this.serviceToPackageMapModel.collection.createIndex(
        { serviceId: 1 },
        { unique: true }
      );

      return createResponse(
        HttpStatus.OK,
        'Unique indexes for collection serviceToPackageMap created with success!'
      );
    } catch (err) {
      return createResponse(
        HttpStatus.BAD_REQUEST,
        `Indexes creating were wrong! ${err?.message}`
      );
    }
  }
}
