/* eslint-disable @typescript-eslint/no-unsafe-return */
import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { ServiceToPackageMapService } from './serviceToPackageMap.service';
import { CreateServiceToPackageMapDto } from './dto/createServiceToPackageMap.dto';
import { UpdateServiceToPackageMapDto } from './dto/updateServiceToPackageMap.dto';
import { ResponseInterface } from 'ads-layouts-tools';

@Controller('serviceToPackageMap')
export class ServiceToPackageMapController {
  constructor(private readonly serviceToPackageMapService: ServiceToPackageMapService) {}

  @Post()
  create(@Body() createServiceToPackageMapDto: CreateServiceToPackageMapDto) {
    return this.serviceToPackageMapService.create(createServiceToPackageMapDto);
  }

  @Delete(':name')
  remove(@Param('name') mapToDelete: string) {
    return this.serviceToPackageMapService.delete(mapToDelete);
  }

  @Get()
  findAll() {
    return this.serviceToPackageMapService.findAll();
  }

  @Get(':name')
  findOne(@Param('name') name: string) {
    return this.serviceToPackageMapService.findOne(name);
  }

  @Patch(':name')
  update(
    @Param('name') name: string,
    @Body() updateServiceToPackageMapDto: UpdateServiceToPackageMapDto
  ) {
    return this.serviceToPackageMapService.update(name, updateServiceToPackageMapDto);
  }

  @Post('unbind/:name')
  unbindServicesFromPackage(
    @Param('name') name: string,
    @Body() updateServiceToPackageMapDto: UpdateServiceToPackageMapDto
  ) {
    return this.serviceToPackageMapService.unbindServicesFromPackage(
      name,
      updateServiceToPackageMapDto.serviceId
    );
  }

  @Post('bind/:name')
  bindServicesFromPackage(
    @Param('name') name: string,
    @Body() updateServiceToPackageMapDto: UpdateServiceToPackageMapDto
  ) {
    return this.serviceToPackageMapService.bindServicesFromPackage(
      name,
      updateServiceToPackageMapDto.serviceId
    );
  }

  @Get('/get-all-indexes')
  async getAllIndexes() {
    return await this.serviceToPackageMapService.getServiceToPackageMapIndexes();
  }

  @Post('/drop-all-indexes')
  async dropAllIndexes(): Promise<ResponseInterface> {
    return await this.serviceToPackageMapService.dropAllIndexes();
  }

  @Post('/create-all-indexes')
  async createAllIndexes() {
    return await this.serviceToPackageMapService.createServiceToPackageMapIndexes();
  }
}
