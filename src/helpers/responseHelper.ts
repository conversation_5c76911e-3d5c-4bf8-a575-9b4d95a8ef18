import { HttpStatus } from '@nestjs/common';
import { ResponseInterface } from 'ads-layouts-tools';
import { getResponseTypeFromStatusCode } from 'Helpers';

export function createResponse(
  statusCode: HttpStatus,
  message: string,
  result?: any,
  raw = false
): ResponseInterface | typeof result {
  return raw
    ? result
    : {
        statusCode,
        type: getResponseTypeFromStatusCode(statusCode),
        message,
        result
      };
}
