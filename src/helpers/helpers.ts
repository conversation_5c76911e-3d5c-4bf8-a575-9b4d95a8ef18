import { HttpStatus } from '@nestjs/common';
import { CreateException } from 'Utils';
import { ResponseType, SearchQueryName } from 'ads-layouts-tools';
import dayjs from 'dayjs';
import { FilterQuery } from 'mongoose';

export function getArrayDifference(arr1: string[], arr2: string[]): string[] {
  return arr1.filter(item => !arr2.includes(item));
}

export function getFetchRuleOptions(
  name: string,
  rulesPackage: string | undefined
): FilterQuery<any> {
  if (rulesPackage) {
    return { name, rulesPackage };
  }

  return {
    name,
    rulesPackage: { $exists: false }
  };
}

export function getLikeQueryOptions<
  T extends SearchQueryName,
  K extends Exclude<keyof T, 'onlyNames'>
>(
  fields: Exclude<keyof T, 'onlyNames'>[],
  searchQuery: T,
  preventProcessAll = false
): Partial<Record<K, object | T[K]>> {
  const options: Record<K, object | T[K]> = {} as Record<K, object | T[K]>;
  const { onlyNames, ...searchQueryCopy } = searchQuery;
  let countEmpty = 0;

  const fieldsArr = [...fields, '_id', 'id'] as K[];

  fieldsArr.forEach(field => {
    const value = searchQueryCopy[field];
    delete searchQueryCopy[field];

    if (field == 'rulesPackage' && value == 'original') {
      options[field] = { $exists: false };
    } else if (value) {
      const match = typeof value === 'string' ? value.match(/^%([^%]*)%$/) : null;

      field = field === ('id' as K) ? ('_id' as K) : field;

      options[field] = match?.[1] ? { $regex: match[1], $options: 'i' } : value;
    } else {
      countEmpty++;
    }
  });

  if (Object.keys(searchQueryCopy).length) {
    throw CreateException({
      message: `Not allowed query params in url [${Object.keys(searchQueryCopy).join(', ')}]. Allowed is [${fieldsArr.join(', ')}] for now.`,
      statusCode: HttpStatus.BAD_REQUEST
    });
  }

  if (countEmpty === fieldsArr.length && preventProcessAll) {
    throw CreateException({
      message: `No search query param provided! Rejected attempt to process all items!`,
      statusCode: HttpStatus.BAD_REQUEST
    });
  }

  return options;
}

export function calculateNextVersion(currentVersion?: string | undefined): string {
  // const regex = /^v\d+_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}$/;
  // regex.test(currentVersion);

  const matchNumber = currentVersion?.match(/^v(\d+)_/);
  const currentVersionNumber = matchNumber ? parseInt(matchNumber[1], 10) + 1 : 1;
  const datePart = dayjs().format('YYYY-MM-DD_HH-mm');

  return `v${currentVersionNumber}_${datePart}`;
}

export function getResponseTypeFromStatusCode(statusCode: HttpStatus): ResponseType {
  let type: ResponseType;

  switch (statusCode) {
    case HttpStatus.OK:
    case HttpStatus.CREATED:
      type = ResponseType.SUCCESS;
      break;
    case HttpStatus.BAD_REQUEST:
    case HttpStatus.INTERNAL_SERVER_ERROR:
    case HttpStatus.SERVICE_UNAVAILABLE:
      type = ResponseType.ERROR;
      break;
    default:
      type = ResponseType.INFO;
      break;
  }

  return type;
}

export function getKeyOfRulesPackageName(functionName: string): string {
  switch (functionName) {
    case 'deleteFoundRulesPackage':
      return 'name';
    default:
      return 'rulesPackage';
  }
}
