import { SearchQueryName } from 'ads-layouts-tools';
import * as <PERSON><PERSON> from 'joi';
import { JoiSchema, JoiSchemaOptions } from 'nestjs-joi';

@JoiSchemaOptions({
  allowUnknown: false,
  cache: true
})
export class searchQueryNameDto implements SearchQueryName {
  @JoiSchema(Joi.string().optional())
  name?: string;

  @JoiSchema(Joi.string().optional())
  rulesPackage?: string;

  @JoiSchema(Joi.string().optional())
  serviceId?: string;

  @JoiSchema(Joi.boolean().optional().default(false))
  onlyNames!: boolean;
}
