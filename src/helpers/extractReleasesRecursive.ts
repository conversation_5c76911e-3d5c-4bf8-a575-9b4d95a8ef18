import { union } from './unionSet';

export const extractReleases = (obj: object): Set<string> => {
  let releases: Set<string> = new Set();

  for (const value of Object.values(obj)) {
    if (typeof value === 'object') {
      const extension = extractReleases(value as object);
      releases = union(releases, extension);
    } else if (typeof value === 'string') {
      releases.add(value);
    }
  }
  return releases;
};
