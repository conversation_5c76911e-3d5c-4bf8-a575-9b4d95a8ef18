import { HttpStatus } from '@nestjs/common';
import { getKeyOfRulesPackageName } from 'Helpers';
import { RulesPackagePointerInterface } from 'InterfacesAndTypes';
import { CreateException } from 'Utils';
import { RulesPackagesService } from '../rulesPackages/rulesPackages.service';

export function useFreeze<PERSON>hecker(rulesPackagePointer: RulesPackagePointerInterface) {
  return function (target: any, functionName: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const rulesPackage = rulesPackagePointer.isSearchQuery
        ? args[rulesPackagePointer.key]?.[getKeyOfRulesPackageName(functionName)]
        : args[rulesPackagePointer.key];

      const service: RulesPackagesService = (
        'rulesPackagesService' in this && this.rulesPackagesService
          ? this.rulesPackagesService
          : this
      ) as RulesPackagesService;

      if (rulesPackage) {
        const rulesPackageFreezeResponse =
          await service.checkIsRulesPackageFreeze(rulesPackage);

        if (rulesPackageFreezeResponse.isFreeze) {
          const isQueryLike = rulesPackage.match(/^%([^%]*)%$/)?.[1];

          const messagePart = isQueryLike ? 'some of rules packages matched to' : '';

          throw CreateException({
            message: `Operation is not allowed because ${messagePart} ${rulesPackage} is frozen! ${isQueryLike ? rulesPackageFreezeResponse.freezeInfo : ''}`,
            statusCode: HttpStatus.BAD_REQUEST
          });
        }
      }

      const result = await originalMethod.apply(this, [...args]);

      if (rulesPackage) {
        await service.rulesPackageVersionUp(rulesPackage);
      }

      return result;
    };

    return descriptor;
  };
}
